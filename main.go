// This APP Use GDP V2

package main

import (
	"context"
	"flag"
	"icode.baidu.com/helix_web/task"
	"log"
	"os"

	_ "icode.baidu.com/baidu/gdp/automaxprocs"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/bootstrap"
	"icode.baidu.com/helix_web/jobs"
)

// 命令行输入参数处理
var config = flag.String("conf", "./conf/app.toml", "app config file")

func main() {
	flag.Parse()
	config, err := bootstrap.ParserAppConfig(*config)
	if err != nil {
		log.Fatalln(err)
	}
	env.Default = config.Env

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	bootstrap.MustInit(ctx)
	app := bootstrap.NewApp(ctx, config)

	// 定时任务
	go jobs.SyncBillingOrders(ctx)
	go jobs.SyncTrial(ctx)
	if env.RunMode() == env.RunModeRelease {
		go task.NewSchedule(ctx).Start(ctx)
	}

	// 退出处理
	exitCode := app.StartWithGracefulShutdown()
	os.Exit(exitCode)
}
