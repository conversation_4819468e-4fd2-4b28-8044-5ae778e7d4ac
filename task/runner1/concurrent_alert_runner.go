package runner1

import (
	"context"
	"encoding/json"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/task/task"
	"strconv"
	"time"
)

const (
	TimeFormat          = "2006-01-02 15:04:05"
	StatusDoing         = 10
	ConcurrentThreshold = 50
)

var (
	taskM   = models.Task{}
	isAlert = false
)

type ConcurrentAlertRunner struct {
	task.Runner
}

func (t *ConcurrentAlertRunner) Run(ctx context.Context, message *task.Message) error {
	now := time.Now()
	taskCond := models.TaskCond{
		Type:        int32(models.TaskTypeHelixFoldAA),
		Status:      StatusDoing,
		GtCreatedAt: now.AddDate(0, 0, -7).Format(TimeFormat),
		LtCreatedAt: now.Format(TimeFormat),
	}
	total, err := taskM.CountByCond(ctx, taskCond)
	if err != nil {
		return err
	}
	if total >= ConcurrentThreshold {
		message.Content = "helixfold3 task count exceeds threshold " + strconv.Itoa(ConcurrentThreshold)
		isAlert = true
	}
	return nil
}

func (t *ConcurrentAlertRunner) ShouldAlert(ctx context.Context) bool {
	return isAlert
}

func (t *ConcurrentAlertRunner) Alert(ctx context.Context, message *task.Message) {
	messageStr, _ := json.Marshal(message)
	go helpers.HelixFold3ConcurrentTaskNotice(ctx,
		"--- helixfold3 task concurrent count alert ---\n"+string(messageStr))
	isAlert = !isAlert
}

func (t *ConcurrentAlertRunner) ExecuteFail(ctx context.Context, err error) {
	go helpers.HelixFold3ConcurrentTaskNotice(ctx, "--- helixfold3 task concurrent count alert fail ---\n"+err.Error())
}
