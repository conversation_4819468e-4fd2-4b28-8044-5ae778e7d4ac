package task

import (
	"context"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/helix_web/models"
)

type Message struct {
	ID        string    `json:"id"`
	From      string    `json:"from"`
	To        string    `json:"to"`
	Content   string    `json:"content"`
	Timestamp time.Time `json:"timestamp"`
}

type Runner interface {
	Run(ctx context.Context, message *Message) error
	ShouldAlert(ctx context.Context) bool
	Alert(ctx context.Context, message *Message)
	ExecuteFail(ctx context.Context, err error)
}

type Task struct {
	Task              *models.Task
	Name              string
	Interval          time.Duration
	Executed          bool
	LastExecutionTime *time.Time
	Runner            Runner
}

func NewTask(ctx context.Context, config Config, runner Runner) *Task {
	return &Task{
		Name:              config.Name,
		Interval:          config.Interval,
		Executed:          false,
		LastExecutionTime: nil,
		Task:              &models.Task{},
		Runner:            runner,
	}
}

func (t *Task) Execute(ctx context.Context) {
	lastTime := t.LastExecutionTime
	currentTime := time.Now()
	if lastTime != nil && currentTime.Sub(*lastTime) < t.Interval {
		return
	}
	t.LastExecutionTime = &currentTime
	message := &Message{
		ID:        t.generateMessageID(),
		From:      t.Name,
		To:        "helix-web",
		Content:   "run method must be implemented by subclass",
		Timestamp: time.Now(),
	}
	err := t.Runner.Run(ctx, message)
	if err != nil {
		t.Runner.ExecuteFail(ctx, err)
	}
	if t.Runner.ShouldAlert(ctx) {
		t.Runner.Alert(ctx, message)
	}
}

func (t *Task) generateMessageID() string {
	return uuid.New().String() + t.Name
}
