package task

import (
	"context"
	"fmt"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/task/runner1"
	"icode.baidu.com/helix_web/task/task"
	"sync"
	"time"

	"github.com/pelletier/go-toml"
)

const (
	Lock                = "lock"
	ExpireSeconds       = 15
	TickerPeriodSeconds = 10
)

var (
	once     sync.Once
	tasks    []*task.Task
	schedule *Scheduler
)

type Scheduler struct{}

func NewSchedule(ctx context.Context) *Scheduler {
	once.Do(func() {
		schedule = &Scheduler{}
		schedule.init(ctx)
	})
	return schedule
}

func (s *Scheduler) init(ctx context.Context) {
	data, _ := toml.LoadFile("conf/time.toml")
	taskConfigs := data.Get("tasks").([]*toml.Tree)
	for _, taskConfig := range taskConfigs {
		config := task.Config{
			Name:     taskConfig.Get("name").(string),
			Interval: time.Duration(taskConfig.Get("interval").(int64)) * time.Second,
			Enabled:  taskConfig.Get("enabled").(bool),
		}
		if config.Enabled {
			var runner task.Runner
			switch config.Name {
			case "ConcurrentAlertTask":
				runner = &runner1.ConcurrentAlertRunner{}
			default:
			}
			if runner != nil {
				task := task.NewTask(ctx, config, runner)
				tasks = append(tasks, task)
			}
		}
	}
}

func (s *Scheduler) Start(ctx context.Context) {
	once.Do(func() {
		s.init(ctx)
	})
	ticker := time.NewTicker(TickerPeriodSeconds * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 执行任务
			s.Run(ctx)
		case <-ctx.Done():
			// 接收到取消信号，退出
			fmt.Println("Scheduler stopped")
			return
		}
	}
}

func (s *Scheduler) Run(ctx context.Context) {
	lock := redis.SetNX(ctx, Lock, "", ExpireSeconds)
	if lock {
		for _, task := range tasks {
			task.Execute(ctx)
		}
	}
}
