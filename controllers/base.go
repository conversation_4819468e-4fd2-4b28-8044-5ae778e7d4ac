package controllers

import (
	"context"
	"errors"
	"strconv"
	"strings"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
)

const (
	DefaultLimit = 10
	DefaultPage  = 1

	MaxLimitNum = 2000
	MaxPageNum  = 100

	UrlLenLimit     = 256
	ContentLenLimit = 1024
	AuthorLenLimit  = 512

	NameLenLimit   = 128
	DescLenLimit   = 128
	RemarkLenLimit = 2048

	OperateTypePublish  = "publish"
	OperateTypeWithdraw = "withdraw"
	OperateTypeHome     = "home"
	OperateTypeUnhome   = "unhome"
	OperateTypeTop      = "top"
	OperateTypeUnTop    = "untop"

	// 10M
	FileContentLimit = 10240000

	PasswordLenLimit = 64
	UsernameLenLimit = 64

	KeywordLenLimit = 128
	IdNumLimit      = 100
	SourceNumLimit  = 20

	VersionLenLimit     = 32
	VersionDescLenLimit = 256
	DatasetDescLimit    = 2000
	DatasetRemarkLimit  = 2000
	TagIdNumLimit       = 20
	FormatLenLimit      = 20
	OwnerLenLimit       = 32
)

// 获得adminId
func getAdminId(ctx context.Context) def.AutoIDType {
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	adminId := adminInfo.AdminId

	return adminId
}

// check 分页
func formatPaging(limit, page int) (int, int) {
	if limit <= 0 || limit > MaxLimitNum {
		limit = DefaultLimit
	}
	if page <= 0 || page > MaxPageNum {
		page = DefaultPage
	}

	return limit, page
}

// source type check
func checkTagType(tType def.EnumType) bool {
	if tType == models.TagTypeObject ||
		tType == models.TagTypeDimension ||
		tType == models.TagTypeFunc {
		return true
	}

	return false
}

// try type check
func checkTryType(tType def.EnumType) bool {
	if tType == models.TrialTypeTime ||
		tType == models.TrialTypeNum {
		return true
	}

	return false
}

// user Type check
func checkUserType(tType def.EnumType) bool {
	if tType == models.UserTypeInside ||
		tType == models.UserTypeNormal ||
		tType == models.UserTypeCharge ||
		tType == models.UserTypeTry {
		return true
	}

	return false
}

func checkAddTryType(tType def.EnumType) bool {
	if tType == models.UserTypeCharge ||
		tType == models.UserTypeTry {
		return true
	}

	return false
}

// source type check
func checkSourceType(sourceType def.EnumType) bool {
	res, _ := models.SourceTypeList[sourceType]
	return res
}

// 检查操作类型
func checkOperateType(operateType string) bool {
	if operateType == OperateTypePublish || operateType == OperateTypeWithdraw {
		return true
	}
	return false
}

// 检查操作类型
func checkFeedbarOperateType(operateType string) bool {
	if operateType == OperateTypePublish ||
		operateType == OperateTypeWithdraw ||
		operateType == OperateTypeHome ||
		operateType == OperateTypeUnhome ||
		operateType == OperateTypeTop ||
		operateType == OperateTypeUnTop {
		return true
	}
	return false
}

// 检查付费类型
func checkChargeTaskType(taskType def.EnumType) bool {
	if taskType == models.TaskTypeRNASerial ||
		taskType == models.TaskTypeProtein ||
		taskType == models.TaskTypeProteinSingle ||
		taskType == models.TaskTypeAdmet ||
		taskType == models.TaskTypeKYKT ||
		taskType == models.TaskTypeFold ||
		taskType == models.TaskTypePartition ||
		taskType == models.TaskType5UTR ||
		taskType == models.TaskTypeProteinComplex ||
		taskType == models.TaskTypeHelixFoldAA ||
		taskType == models.TaskTypeHelixVSSyn ||
		taskType == models.TaskTypeHelixVS {
		return true
	}

	return false
}

// 检查任务类型
func checkTaskType(tType def.EnumType) bool {
	res, _ := models.TaskTypeList[tType]
	return res
}

// check funcType
func checkFuncType(funcType def.EnumType) bool {
	if funcType == models.FuncTypeTrain || funcType == models.FuncTypeTrainClassify ||
		funcType == models.FuncTypeForecast || funcType == models.FuncTypePretreat ||
		funcType == models.FuncTypeNoStruct || funcType == models.FuncTypeForecastRNA ||
		funcType == models.SerialFuncTypeForecast3UTR || funcType == models.SerialFuncTypeForecast5UTR ||
		funcType == models.FuncTypeForecastCodon {
		return true
	}

	return false
}

// check train_type
func checkTrainType(tType def.EnumType) bool {
	if tType == models.TaskTypeSelfAdmet || tType == models.TaskTypeMolActivity {
		return true
	}

	return false
}

// check dataType
func checkDataType(dataType def.EnumType) bool {
	if dataType == models.DataTypeString ||
		dataType == models.DataTypeFile ||
		dataType == models.DataTypeFileMore {
		return true
	}

	return false
}

// check feedbarType
func checkFeedbarType(feedbarType def.EnumType) bool {
	res, _ := models.FeedbarTypeList[feedbarType]
	return res
}

// check feedbackStatus
func checkFeedbackStatus(status def.EnumType) bool {
	if status == models.FeedbackStatusNew ||
		status == models.FeedbackStatusDone ||
		status == models.FeedbackStatusDel {
		return true
	}

	return false
}

// 获取上传文件内容
func getFileContent(fileUrl string) string {
	bucket, object := helpers.DealBosFileUrl(fileUrl)
	if len(bucket) == 0 || len(object) == 0 {
		return ""
	}

	contentStr, err := bce.GetObject(bucket, object)
	if err != nil || len(contentStr) == 0 {
		return ""
	}

	contentStr = strings.Trim(contentStr, " ")
	return contentStr
}

// 检查文件内容
func checkFileContent(content string) error {
	contentLen := len(content)
	if contentLen == 0 || contentLen > FileContentLimit {
		return errors.New("upload file error")
	}

	return nil
}

// 获取user Map
func getUserMap(ctx context.Context, userIdList []int64) map[int]models.User {
	userMap := make(map[int]models.User, len(userIdList))

	userM := models.User{}
	userList, err := userM.GetByIds(ctx, userIdList)
	if err != nil {
		return userMap
	}

	for _, user := range userList {
		userMap[int(user.ID)] = user
	}
	return userMap
}

// 获取user Map
func getUserMapByRealIds(ctx context.Context, realIdList []int64) map[int]models.User {
	userMap := make(map[int]models.User, len(realIdList))

	userM := models.User{}
	userList, err := userM.GetByRealIds(ctx, realIdList)
	if err != nil {
		return userMap
	}

	for _, user := range userList {
		userMap[int(user.RealID)] = user
	}
	return userMap
}

// 获取task Map
func getTaskMap(ctx context.Context, taskIdList []int64) map[int]models.Task {
	taskMap := make(map[int]models.Task, len(taskIdList))

	taskM := models.Task{}
	taskList, err := taskM.GetByIds(ctx, taskIdList)
	if err != nil {
		return taskMap
	}

	for _, task := range taskList {
		taskMap[int(task.ID)] = task
	}
	return taskMap
}

// 获取admin Map
func getAdminMap(ctx context.Context, adminIdList []int64) map[int]string {
	adminMap := make(map[int]string)

	adminM := models.Admin{}
	adminList, err := adminM.GetByIds(ctx, adminIdList)
	if err != nil {
		return adminMap
	}

	for _, admin := range adminList {
		adminMap[int(admin.ID)] = admin.Username
	}

	return adminMap
}

// 获取 all admin Map
func getAllAdminMap(ctx context.Context) map[int]string {
	adminMap := make(map[int]string)

	adminM := models.Admin{}
	adminList, err := adminM.GetList(ctx, []int64{})
	if err != nil {
		return adminMap
	}

	for _, admin := range adminList {
		adminMap[int(admin.ID)] = admin.Username
	}
	return adminMap
}

// 删除token
func delAdminToken(ctx context.Context, adminId int64) bool {
	token := helpers.Md5(strconv.Itoa(int(adminId)) + TokenSalt)
	cacheKey := redis.AdminTokenPrefix + token

	return redis.Del(ctx, cacheKey)
}
func delAdminInfo(ctx context.Context, adminId int64) bool {
	cacheKey := redis.AdminInfoPrefix + strconv.Itoa(int(adminId))
	return redis.Del(ctx, cacheKey)
}

func getFilterUserId(ctx context.Context) []string {
	openRes := redis.Get(ctx, redis.UserWhiteSwitchKey)
	if openRes != "1" {
		return []string{}
	}

	return redis.SMembers(ctx, redis.UserWhiteKey)
}
