package controllers

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

type projectListParams struct {
	Limit        int            `json:"limit"`
	Page         int            `json:"page"`
	UserKey      string         `json:"user_keyword"`
	Keyword      string         `json:"keyword"`
	CaseTypeList []def.EnumType `json:"case_type_list"`
	MinTime      int64          `json:"min_time"`
	MaxTime      int64          `json:"max_time"`
}

func ListProject(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params projectListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)

	minTime := params.MinTime
	maxTime := params.MinTime
	keyword := helpers.Trim(params.Keyword)
	userKey := helpers.Trim(params.UserKey)
	if maxTime > 0 {
		maxTime = maxTime + 86400
	}

	// 参数check
	limit, page := formatPaging(params.Limit, params.Page)

	// 检索用户
	var userIdList []int64
	if userKey != "" {
		userCond := models.UserCond{
			Keyword: []string{userKey},
		}
		userM := models.User{}
		userList, _ := userM.GetList(ctx, userCond, 100, 1)
		for _, user := range userList {
			userIdList = append(userIdList, int64(user.ID))
		}
		if len(userIdList) == 0 {
			return helpers.SuccReturn(nil)
		}
	}

	// 获取任务信息
	cond := models.ProjectCond{
		UserIdList:       userIdList,
		FilterUserIdList: getFilterUserId(ctx),
		CaseTypeList:     params.CaseTypeList,
		Keyword:          keyword,
		GtCreatedAt:      helpers.UnixToTimeStr(minTime),
		LtCreatedAt:      helpers.UnixToTimeStr(maxTime),
	}

	projectM := models.Project{}
	total, err := projectM.CountByCond(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	taskList, err := projectM.GetByCond(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	var userIdArr []int64
	for _, task := range taskList {
		userIdArr = append(userIdArr, int64(task.UserId))
	}
	userMap := getUserMap(ctx, userIdArr)

	// 数据组装
	var items []any
	for _, task := range taskList {
		tmp := task.SwapData()
		tmp["username"] = ""
		tmp["user_type"] = 0
		tmp["user_remark"] = ""
		if user, ok := userMap[int(task.UserId)]; ok {
			tmp["username"] = user.Username
			tmp["user_type"] = user.Type
			tmp["user_remark"] = user.Remark
		}

		items = append(items, tmp)
	}

	result := map[string]any{
		"items": items,
		"total": total,
	}
	return helpers.SuccReturn(result)
}
