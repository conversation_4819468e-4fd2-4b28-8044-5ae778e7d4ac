package controllers

import (
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

type bulletinListParams struct {
	Limit int `json:"limit"`
	Page  int `json:"page"`
}

func GetBulletinList(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params bulletinListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit := params.Limit
	page := params.Page

	// 参数check
	limit, page = formatPaging(limit, page)

	bulletinM := models.Bulletin{}
	total, err := bulletinM.CountByCond(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	bulletinList, err := bulletinM.GetList(ctx, limit, page, "sort, id desc")
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据组装
	adminIdList := helpers.ColumnList(bulletinList, func(item models.Bulletin) int64 { return item.AdminId })
	adminMap := getAdminMap(ctx, adminIdList)

	var items []interface{}
	for _, bulletin := range bulletinList {
		tmp := bulletin.SwapData()

		tmp["admin_name"] = ""
		if _, ok := adminMap[int(bulletin.AdminId)]; ok {
			tmp["admin_name"] = adminMap[int(bulletin.AdminId)]
		}
		items = append(items, tmp)
	}

	result := map[string]interface{}{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

type addBulletinParams struct {
	ShowTime  int64          `json:"show_time"`
	Content   string         `json:"content"`
	ContentEN string         `json:"content_en"`
	Sort      def.EnumType   `json:"sort"`
	Id        def.AutoIDType `json:"id"`
	Url       string         `json:"url"`
}

func CreateBulletin(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addBulletinParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	content := params.Content
	contentEN := params.ContentEN
	sort := params.Sort
	url := params.Url

	// 参数check
	if params.ShowTime <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "show_time params error")
	}

	contentLen := len([]rune(content))
	if contentLen <= 0 || contentLen > ContentLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "content params error")
	}
	contentENLen := len([]rune(contentEN))
	if contentENLen <= 0 || contentENLen > ContentLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "content_en params error")
	}
	if len([]rune(url)) > UrlLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "url params error")
	}
	if sort <= 0 || sort > 100 {
		sort = 100
	}

	dateT := time.Unix(params.ShowTime, 0).Format("2006-01-02 15:04:05")
	formatTime, _ := time.ParseInLocation("2006-01-02 15:04:05", dateT, time.Local)

	adminInfo := ctx.Value("admin_info").(models.AdminInfo)

	// 添加数据
	bulletinNew := models.Bulletin{
		ShowTime:  formatTime,
		Content:   content,
		ContentEN: contentEN,
		Url:       url,
		Sort:      sort,
		AdminId:   adminInfo.AdminId,
	}
	_, err := bulletinNew.Add(ctx, bulletinNew)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	return helpers.SuccReturn(nil)
}

// 设置动态栏
type setBulletinParams struct {
	Id      def.AutoIDType `json:"id"`
	Operate string         `json:"operate"`
}

func SetBulletin(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params setBulletinParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	operate := params.Operate

	// 参数check
	if !checkFeedbarOperateType(operate) {
		return helpers.FailReturn(helpers.ParamErrorCode, "operate_type params error")
	}
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "id params error")
	}

	bulletinM := models.Bulletin{}
	raw, err := bulletinM.GetById(ctx, params.Id)
	if err != nil || raw.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	switch operate {
	case OperateTypePublish:
		raw.Status = models.StatusOnline
		break
	case OperateTypeWithdraw:
		raw.Status = models.StatusNew
		break
	}
	err = raw.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}
