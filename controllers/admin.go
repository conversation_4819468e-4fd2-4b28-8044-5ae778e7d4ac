package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

// 打点统计
type dotCountParam struct {
	MinTime  def.TimestampType `json:"min_time"`
	MaxTime  def.TimestampType `json:"max_time"`
	TypeList []def.EnumType    `json:"type_list"`
}

func DotList(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params dotCountParam
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	minTime := params.MinTime
	maxTime := params.MaxTime
	typeList := params.TypeList
	if maxTime > 0 {
		maxTime = maxTime + 86400
	}

	// 获取打点
	dotCond := models.DotCond{
		MinTime:  helpers.UnixToDateStr(minTime),
		MaxTime:  helpers.UnixToDateStr(maxTime),
		TypeList: typeList,
	}
	dotM := models.DotData{}
	list, err := dotM.GetList(ctx, dotCond, 100000, 1)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var items []any
	for _, dotItem := range list {
		items = append(items, dotItem.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(result)
}

func DotCount(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params dotCountParam
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	minTime := params.MinTime
	maxTime := params.MaxTime
	if maxTime > 0 {
		maxTime = maxTime + 86400
	}

	// 获取活跃用户
	reqCond := models.RequestCond{
		GtCreatedAt: helpers.UnixToDateStr(minTime),
		LtCreatedAt: helpers.UnixToDateStr(maxTime),
	}
	requestM := models.RequestLog{}
	reqCount, err := requestM.CountByCond(ctx, reqCond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 获取新增用户
	userCond := models.UserCond{
		GtCreatedAt: helpers.UnixToDateStr(minTime),
		LtCreatedAt: helpers.UnixToDateStr(maxTime),
	}
	userM := models.User{}
	userCount, err := userM.Count(ctx, userCond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 获取打点数据
	dotCond := models.DotCond{
		MinTime: helpers.UnixToDateStr(minTime),
		MaxTime: helpers.UnixToDateStr(maxTime),
	}
	dotM := models.DotData{}
	dotRes, err := dotM.Sum(ctx, dotCond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var uv def.CountType
	dotMap := make(map[def.EnumType]def.CountType)
	for _, item := range dotRes {
		dotMap[item.Type] = item.Num
		if item.Type == models.DotTypePV {
			uv = item.UV
		}
	}

	res := map[string]any{
		"active_user":  reqCount,
		"new_user":     userCount,
		"uv":           uv,
		"pv":           helpers.GetValByKey(dotMap, models.DotTypePV),
		"arouse_login": helpers.GetValByKey(dotMap, models.DotTypeHQDL),
		"succ_login":   helpers.GetValByKey(dotMap, models.DotTypeWCDL),
	}
	return helpers.SuccReturn(res)
}

func DotCountCSV(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params dotCountParam
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	minTime := params.MinTime
	maxTime := params.MaxTime
	if maxTime > 0 {
		maxTime = maxTime + 86400
	}
	if maxTime-minTime > 86400*30 {
		return helpers.FailReturn(helpers.ParamErrorCode, "时间周期最长为30天")
	}

	// 获取活跃用户
	reqCond := models.RequestCond{
		GtCreatedAt: helpers.UnixToDateStr(minTime),
		LtCreatedAt: helpers.UnixToDateStr(maxTime),
	}
	requestM := models.RequestLog{}
	reqSumList, err := requestM.SumByCond(ctx, reqCond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 获取新增用户
	userCond := models.UserCond{
		GtCreatedAt: helpers.UnixToDateStr(minTime),
		LtCreatedAt: helpers.UnixToDateStr(maxTime),
	}
	userM := models.User{}
	userSumList, err := userM.SumByCond(ctx, userCond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 获取打点数据
	dotCond := models.DotCond{
		MinTime: helpers.UnixToDateStr(minTime),
		MaxTime: helpers.UnixToDateStr(maxTime),
	}
	dotM := models.DotData{}
	dotSumList, err := dotM.SumByCond(ctx, dotCond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	reqMap := helpers.KeyBy(reqSumList, func(item models.ReqSumRes) int {
		return int(item.Date.Unix())
	})
	userMap := helpers.KeyBy(userSumList, func(item models.UserSumRes) int {
		return int(item.Date.Unix())
	})

	dotUVMap := make(map[int]def.CountType)
	dotPVMap := dotUVMap
	dotALMap := dotUVMap
	dotSLMap := dotUVMap
	for _, dotSum := range dotSumList {
		switch dotSum.Type {
		case models.DotTypePV:
			dotPVMap[int(dotSum.Date.Unix())] = dotSum.Num
			dotUVMap[int(dotSum.Date.Unix())] = dotSum.UV
			break
		case models.DotTypeHQDL:
			dotALMap[int(dotSum.Date.Unix())] = dotSum.Num
			break
		case models.DotTypeWCDL:
			dotSLMap[int(dotSum.Date.Unix())] = dotSum.Num
			break
		}
	}

	var items []any
	for minTime < maxTime {
		tmp := map[string]any{
			"date":         helpers.UnixToDateStr(minTime),
			"active_user":  helpers.GetValByKey(reqMap, int(minTime)).Sum,
			"new_user":     helpers.GetValByKey(userMap, int(minTime)).Sum,
			"uv":           helpers.GetValByKey(dotUVMap, int(minTime)),
			"pv":           helpers.GetValByKey(dotPVMap, int(minTime)),
			"arouse_login": helpers.GetValByKey(dotALMap, int(minTime)),
			"succ_login":   helpers.GetValByKey(dotSLMap, int(minTime)),
		}
		items = append(items, tmp)

		minTime += 86400
	}
	res := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(res)
}

// GetPlatformData 获取用户拥有的菜单
func GetPlatformData(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 数据统计
	userM := models.User{}
	var userCond models.UserCond
	totalUserNum, _ := userM.Count(ctx, userCond)

	userCond.GtCreatedAt = helpers.UnixToDateStr(time.Now().Unix() - 7*86400)
	weekUserNum, _ := userM.Count(ctx, userCond)

	userCond.GtCreatedAt = helpers.UnixToDateStr(time.Now().Unix() - 30*86400)
	monthUserNum, _ := userM.Count(ctx, userCond)

	res := map[string]def.CountType{
		"total_user_num": totalUserNum,
		"week_user_num":  weekUserNum,
		"month_user_num": monthUserNum,
	}
	return helpers.SuccReturn(res)
}

// 获取用户拥有的菜单
func GetAdminMenu(ctx context.Context, req ghttp.Request) ghttp.Response {
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)

	roleM := models.Role{}
	roleInfo, err := roleM.GetById(ctx, adminInfo.RoleId)
	if err != nil || roleInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var menuIdList []def.CountType
	_ = json.Unmarshal([]byte(roleInfo.MenuIds), &menuIdList)

	menuM := models.Menu{}
	menuList, err := menuM.GetByIds(ctx, menuIdList)
	if err != nil || len(menuList) <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 获取一级菜单
	var tmpMenuList []any
	for _, item := range menuList {
		var permissionIdList []def.CountType
		_ = json.Unmarshal([]byte(item.PermissionIds), &permissionIdList)

		nn := helpers.SliceIntersect(adminInfo.PermissionIds, permissionIdList)
		if len(nn) == 0 {
			continue
		}

		tmpMenuList = append(tmpMenuList, item.SwapData())
	}

	// 目前最多到二级，先这样处理
	//var items []map[string]any
	//for _, tempMenu := range tmpMenuList {
	//	var childList []any
	//	for _, item := range menuList {
	//		if item.ParentId == tempMenu.ID {
	//			childList = append(childList, item.SwapData())
	//		}
	//	}
	//
	//	tmpItem := tempMenu.SwapData()
	//	tmpItem["child"] = childList
	//	items = append(items, tmpItem)
	//}

	result := map[string]any{
		"items": tmpMenuList,
	}
	return helpers.SuccReturn(result)
}

// 获取用户信息
// 从 ctx 中获取
func GetAdminInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)

	return helpers.SuccReturn(adminInfo)
}

// 管理员更新自己的密码
type updatePassportParams struct {
	OldPassword string `json:"old_password"`
	NewPassword string `json:"new_password"`
}

func UpdatePassport(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params updatePassportParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	oldPassword := params.OldPassword
	newPassword := params.NewPassword

	// check old and new pass
	if oldPassword == newPassword {
		return helpers.FailReturn(helpers.ParamErrorCode, "old and new passwords cannot same")
	}

	adminM := models.Admin{}
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	adminData, err := adminM.GetById(ctx, adminInfo.AdminId)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	checkRes := adminM.CheckPassword(oldPassword, adminData.Password)
	if !checkRes {
		return helpers.FailReturn(helpers.LogicErrorCode, "old password error")
	}

	adminData.Password = adminM.EncryptPassword(newPassword)
	err = adminData.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 删除token
	delAdminToken(ctx, adminInfo.AdminId)
	return helpers.SuccReturn(nil)
}

// ---------- super admin 权限
// ---------- db admin
// 获取管理员列表
func GetAdminList(ctx context.Context, req ghttp.Request) ghttp.Response {
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)

	var roleIdList []int64
	if adminInfo.RoleAlias != models.AliasSuper {
		roleM := models.Role{}
		roleList, err := roleM.GetList(ctx, []string{models.AliasDBManage, models.AliasDB})
		if err != nil || len(roleList) <= 0 {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}

		for _, roleInfo := range roleList {
			roleIdList = append(roleIdList, int64(roleInfo.ID))
		}
	}

	adminM := models.Admin{}
	adminList, err := adminM.GetList(ctx, roleIdList)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	permM := models.Permission{}
	permList, err := permM.GetList(ctx)
	if err != nil || len(permList) <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据处理
	permMap := make(map[int64]any)
	for _, perm := range permList {
		permMap[int64(perm.ID)] = perm.SwapData()
	}

	var items []any
	for _, admin := range adminList {
		var permIdList []int64
		_ = json.Unmarshal([]byte(admin.PermissionIds), &permIdList)

		var permInfoList []any
		for _, permId := range permIdList {
			if _, ok := permMap[permId]; !ok {
				continue
			}
			permInfoList = append(permInfoList, permMap[permId])
		}

		tmpAdmin := admin.SwapData()
		tmpAdmin["permission_list"] = permInfoList
		items = append(items, tmpAdmin)
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 添加管理员
type addAdminParams struct {
	Username      string  `json:"username"`
	Password      string  `json:"password"`
	CheckPassword string  `json:"check_password"`
	RoleId        int64   `json:"role_id"`
	PermissionIds []int64 `json:"permission_ids"`
}

func AddAdmin(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addAdminParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	username := params.Username
	password := params.Password
	checkPassword := params.CheckPassword
	roleId := params.RoleId
	permissionIds := params.PermissionIds

	// check
	usernameLen := len(username)
	if usernameLen <= 0 || usernameLen > UsernameLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "username len error")
	}
	passwordLen := len(password)
	if passwordLen <= 0 || passwordLen > PasswordLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "password len error")
	}
	if password != checkPassword {
		return helpers.FailReturn(helpers.ParamErrorCode, "password and check_password not the same")
	}

	// check role and permission
	err := checkRoleAndPermission(ctx, roleId, permissionIds)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, err.Error())
	}

	// check username
	adminM := models.Admin{}
	adminRow, err := adminM.GetByUsername(ctx, username)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if adminRow.ID > 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "username repeat")
	}

	// 添加数据
	permissionIdByte, _ := json.Marshal(permissionIds)
	adminNew := models.Admin{
		Username:      username,
		Password:      adminM.EncryptPassword(password),
		RoleId:        roleId,
		PermissionIds: string(permissionIdByte),
	}
	_, err = adminM.Add(ctx, adminNew)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 添加日志
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	roleName := getRoleName(ctx, roleId)
	addAdminLog(ctx, adminInfo.AdminId, "添加"+roleName+": "+username)

	return helpers.SuccReturn(nil)
}

// 更新管理员
type updateAdminParams struct {
	AdminId       int64   `json:"admin_id"`
	RoleId        int64   `json:"role_id"`
	PermissionIds []int64 `json:"permission_ids"`
}

func UpdateAdmin(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params updateAdminParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	adminId := params.AdminId
	roleId := params.RoleId
	permissionIds := params.PermissionIds

	// check role and permission
	err := checkRoleAndPermission(ctx, roleId, permissionIds)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, err.Error())
	}

	// check adminId
	adminM := models.Admin{}
	adminData, err := adminM.GetById(ctx, adminId)
	if err != nil || adminData.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	permissionIdByte, _ := json.Marshal(permissionIds)
	adminData.RoleId = roleId
	adminData.PermissionIds = string(permissionIdByte)
	err = adminData.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	roleName := getRoleName(ctx, roleId)
	addAdminLog(ctx, adminInfo.AdminId, "变更"+roleName+": "+adminData.Username)

	// 删除token
	delAdminToken(ctx, adminId)
	delAdminInfo(ctx, adminId)
	return helpers.SuccReturn(nil)
}

// 更新管理员
type resetAdminPassParams struct {
	AdminId  int64  `json:"admin_id"`
	Password string `json:"password"`
}

func ResetAdminPassport(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params resetAdminPassParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	adminId := params.AdminId
	password := params.Password

	// check
	passwordLen := len(password)
	if passwordLen <= 0 || passwordLen > PasswordLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "password len error")
	}

	// check adminId
	adminM := models.Admin{}
	adminData, err := adminM.GetById(ctx, adminId)
	if err != nil || adminData.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	adminData.Password = adminM.EncryptPassword(password)
	err = adminData.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 删除token
	delAdminToken(ctx, adminId)
	return helpers.SuccReturn(nil)
}

// del admin
type delAdminParams struct {
	AdminId int64 `json:"admin_id"`
}

func DelAdmin(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params delAdminParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	adminId := params.AdminId

	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	if adminInfo.AdminId == adminId {
		return helpers.SuccReturn(nil)
	}

	adminM := models.Admin{}
	adminData, err := adminM.GetById(ctx, adminId)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	if adminData.ID > 0 {
		// 数据库管理员判断
		if adminInfo.RoleAlias == models.AliasDBManage {
			roleM := models.Role{}
			roleInfo, err := roleM.GetById(ctx, int64(adminData.RoleId))
			if err != nil || roleInfo.ID <= 0 {
				return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
			}

			if roleInfo.Alias != models.AliasDB {
				helpers.FailReturn(helpers.LogicErrorCode, "permission is error")
			}
		}

		adminData.Status = models.AdminStatusDel
		err = adminData.Save(ctx)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}

		// 写入log
		roleName := getRoleName(ctx, int64(adminData.RoleId))
		addAdminLog(ctx, adminInfo.AdminId, "删除："+roleName+": "+adminData.Username)
	}

	// 删除token
	delAdminToken(ctx, adminId)
	return helpers.SuccReturn(nil)
}

// check permission
func checkRoleAndPermission(ctx context.Context, roleId int64, permissionIds []int64) error {
	roleM := models.Role{}
	roleInfo, err := roleM.GetById(ctx, roleId)
	if err != nil || roleInfo.ID <= 0 {
		return errors.New("data error, please repeat")
	}

	// check permission
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	if adminInfo.RoleAlias == models.AliasDBManage && roleInfo.Alias != models.AliasDB {
		return errors.New("permission id error")
	}

	var menuIdList []int64
	_ = json.Unmarshal([]byte(roleInfo.MenuIds), &menuIdList)

	menuM := models.Menu{}
	menuList, err := menuM.GetByIds(ctx, menuIdList)
	if err != nil || len(menuList) <= 0 {
		return errors.New("data error, please repeat")
	}

	var permIdList []int64
	for _, menu := range menuList {
		var tmpIdList []int64
		_ = json.Unmarshal([]byte(menu.PermissionIds), &tmpIdList)

		permIdList = append(permIdList, tmpIdList...)
	}

	for _, val := range permissionIds {
		checkRes := helpers.Contain(permIdList, val)
		if !checkRes {
			return errors.New("permission id error")
		}
	}

	return nil
}

// 管理员权限
// 获取角色列表
func GetRoleList(ctx context.Context, req ghttp.Request) ghttp.Response {
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)

	// 角色别名检索
	aliasList := []string{models.AliasDB, models.AliasDBManage}
	if adminInfo.RoleAlias == models.AliasSuper {
		aliasList = append(aliasList, models.AliasPlatform, models.AliasSuper)
	}

	roleM := models.Role{}
	roleList, err := roleM.GetList(ctx, aliasList)
	if err != nil || len(roleList) <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 菜单
	menuM := models.Menu{}
	menuList, err := menuM.GetList(ctx)
	if err != nil || len(menuList) <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 权限
	permM := models.Permission{}
	permList, err := permM.GetList(ctx)
	if err != nil || len(permList) <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据处理
	permMap := make(map[int64]any)
	for _, perm := range permList {
		permMap[int64(perm.ID)] = perm.SwapData()
	}

	menuMap := make(map[int64]any)
	for _, menu := range menuList {
		var permIdList []int64
		_ = json.Unmarshal([]byte(menu.PermissionIds), &permIdList)

		var permInfoList []any
		for _, permId := range permIdList {
			if _, ok := permMap[permId]; !ok {
				continue
			}
			permInfoList = append(permInfoList, permMap[permId])
		}

		tmpMenu := menu.SwapData()
		tmpMenu["permission_list"] = permInfoList
		menuMap[menu.ID] = tmpMenu
	}

	var items []any
	for _, role := range roleList {
		var menuIdList []int64
		_ = json.Unmarshal([]byte(role.MenuIds), &menuIdList)

		var menuInfoList []any
		for _, menuId := range menuIdList {
			if _, ok := menuMap[menuId]; !ok {
				continue
			}
			menuInfoList = append(menuInfoList, menuMap[menuId])
		}

		tmpRole := role.SwapData()
		tmpRole["menu_list"] = menuInfoList
		items = append(items, tmpRole)
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 添加操作日志
func addAdminLog(ctx context.Context, adminId int64, content string) {
	datasetLogNew := models.DatasetLog{
		AdminId:     adminId,
		OperateType: models.OperateTypeManage,
		Content:     content,
	}

	_, _ = datasetLogNew.Add(ctx, datasetLogNew)
}

// 获取角色名称
func getRoleName(ctx context.Context, roleId int64) string {
	roleM := models.Role{}
	role, err := roleM.GetById(ctx, roleId)
	if err != nil || role.ID <= 0 {
		return ""
	}

	return role.Name
}
