package controllers

import (
	"context"
	"encoding/json"
	"github.com/go-playground/validator/v10"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

type CoopListParams struct {
	Limit        def.IntType  `json:"limit"`
	Page         def.IntType  `json:"page"`
	MinTime      int64        `json:"min_time"`
	MaxTime      int64        `json:"max_time"`
	CoopType     def.EnumType `json:"coop_type"`
	CustomerType def.EnumType `json:"customer_type"`
	BudgetType   def.EnumType `json:"budget_type"`
	ProductList  []string     `json:"product_list"`
}

func ListCoop(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params CoopListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	minCreateTime := params.MinTime
	maxCreateTime := params.MaxTime
	if maxCreateTime > 0 {
		maxCreateTime = maxCreateTime + 86400
	}
	limit, page := formatPaging(params.Limit, params.Page)

	// 获取信息
	cond := models.CoopCond{
		CoopType:     params.CoopType,
		BudgetType:   params.BudgetType,
		CustomerType: params.CustomerType,
		MinTime:      helpers.UnixToTimeStr(minCreateTime),
		MaxTime:      helpers.UnixToTimeStr(maxCreateTime),
	}

	coopM := models.Coop{}
	total, err := coopM.CountByCond(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	coopList, err := coopM.GetList(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据组装
	var adminIdList []int64
	var userIdList []int64
	for _, coop := range coopList {
		adminIdList = append(adminIdList, int64(coop.AdminId))
		userIdList = append(userIdList, int64(coop.UserId))
	}
	adminMap := getAdminMap(ctx, adminIdList)
	userMap := getUserMap(ctx, userIdList)

	var items []interface{}
	for _, coop := range coopList {
		tmp := coop.SwapData()

		tmp["admin_name"] = helpers.GetValByKey(adminMap, int(coop.AdminId))

		userTmp := helpers.GetValByKey(userMap, int(coop.UserId))
		tmp["username"] = userTmp.Username
		tmp["user_remark"] = userTmp.Remark
		items = append(items, tmp)
	}

	result := map[string]interface{}{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 合作咨询
type coopUpdateParams struct {
	Id     int64        `json:"id" validate:"required,min=1"`
	Remark string       `json:"remark" validate:"required,max=2048"`
	Status def.EnumType `json:"status" validate:"required"`
}

func UpdateCoop(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params coopUpdateParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	validateN := validator.New()
	err := validateN.Struct(params)
	if err != nil {
		return helpers.FailReturn(helpers.ParamErrorCode, helpers.Validate(err))
	}
	if !checkFeedbackStatus(params.Status) {
		return helpers.FailReturn(helpers.ParamErrorCode, "status params error")
	}

	// 添加数据
	coopM := models.Coop{}
	coopInfo, err := coopM.GetById(ctx, params.Id)
	if err != nil || coopInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	coopInfo.Status = params.Status
	coopInfo.Remark = params.Remark
	err = coopInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}
