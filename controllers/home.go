package controllers

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

func ListOverall(ctx context.Context, req ghttp.Request) ghttp.Response {
	overallM := models.HomeModule{}
	overallList, err := overallM.GetAll(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var item []any
	for _, overall := range overallList {
		item = append(item, overall.SwapData())
	}

	result := map[string]any{
		"items": item,
	}
	return helpers.SuccReturn(result)
}

type overallAddParams struct {
	Type def.EnumType `json:"type"`
	Data []any        `json:"data"`
}

func AddOverall(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params overallAddParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	tType := params.Type
	data := params.Data
	if !checkModuleType(tType) || len(data) <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "param error")
	}

	moduleM := models.HomeModule{}
	moduleInfo, err := moduleM.GetByType(ctx, tType)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if moduleInfo.ID > 0 {
		moduleInfo.Status = models.StatusDel
		moduleInfo.AdminId = getAdminId(ctx)
		err := moduleInfo.Save(ctx)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}
	}

	dataByte, _ := json.Marshal(data)
	moduleNew := models.HomeModule{
		AdminId: getAdminId(ctx),
		Type:    tType,
		Data:    string(dataByte),
	}
	_, err = moduleNew.Add(ctx, moduleNew)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	return helpers.SuccReturn(nil)
}

func checkModuleType(moduleType def.EnumType) bool {
	res, _ := models.ModuleTypeMap[moduleType]
	return res
}
