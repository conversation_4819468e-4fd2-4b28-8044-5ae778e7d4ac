package controllers

import (
	"context"
	"encoding/json"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

type feedbackListParams struct {
	Limit         int          `json:"limit"`
	Page          int          `json:"page"`
	MinCreateTime int64        `json:"min_create_time"`
	MaxCreateTime int64        `json:"max_create_time"`
	Keyword       string       `json:"keyword"`
	Status        def.EnumType `json:"status"`
}

func ListFeedback(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params feedbackListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	status := params.Status
	minCreateTime := params.MinCreateTime
	maxCreateTime := params.MaxCreateTime
	keyword := helpers.Trim(params.Keyword)
	if maxCreateTime > 0 {
		maxCreateTime = maxCreateTime + 86400
	}

	// 参数check
	limit, page := formatPaging(params.Limit, params.Page)

	// 获取信息
	cond := models.FeedbackCond{
		Status:      status,
		Keyword:     keyword,
		GtCreatedAt: helpers.UnixToTimeStr(minCreateTime),
		LtCreatedAt: helpers.UnixToTimeStr(maxCreateTime),
	}

	feedbackM := models.Feedback{}
	total, err := feedbackM.CountByCond(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	feedbackList, err := feedbackM.GetList(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据组装
	var adminIdList []int64
	for _, feedback := range feedbackList {
		adminIdList = append(adminIdList, int64(feedback.AdminId))
	}
	adminMap := getAdminMap(ctx, adminIdList)

	var items []interface{}
	for _, feedback := range feedbackList {
		tmp := feedback.SwapData()

		tmp["admin_name"] = ""
		if _, ok := adminMap[int(feedback.AdminId)]; ok {
			tmp["admin_name"] = adminMap[int(feedback.AdminId)]
		}
		items = append(items, tmp)
	}

	result := map[string]interface{}{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 更新动态栏
type feedbackUpdateParams struct {
	Id     int64        `json:"id"`
	Remark string       `json:"remark"`
	Status def.EnumType `json:"status"`
}

func UpdateFeedback(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params feedbackUpdateParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	status := params.Status
	remark := helpers.Trim(params.Remark)

	// 参数check
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "id params error")
	}
	if !checkFeedbackStatus(status) {
		return helpers.FailReturn(helpers.LogicErrorCode, "type params error")
	}
	if len(remark) > RemarkLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "remark length limit")
	}

	// 添加数据
	feedbackM := models.Feedback{}
	feedbackInfo, err := feedbackM.GetById(ctx, params.Id)
	if err != nil || feedbackInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	feedbackInfo.Status = status
	feedbackInfo.Remark = remark
	err = feedbackInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}

type portraitListParams struct {
	Limit         int    `json:"limit"`
	Page          int    `json:"page"`
	MinCreateTime int64  `json:"min_time"`
	MaxCreateTime int64  `json:"max_time"`
	Keyword       string `json:"keyword"`
}

func ListPortrait(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params portraitListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	minCreateTime := params.MinCreateTime
	maxCreateTime := params.MaxCreateTime
	keyword := helpers.Trim(params.Keyword)
	if maxCreateTime > 0 {
		maxCreateTime = maxCreateTime + 86400
	}

	// 参数check
	limit, page := formatPaging(params.Limit, params.Page)

	// 获取信息
	cond := models.PortraitCond{
		Keyword:          keyword,
		GtCreatedAt:      helpers.UnixToTimeStr(minCreateTime),
		LtCreatedAt:      helpers.UnixToTimeStr(maxCreateTime),
		FilterUserIdList: getFilterUserId(ctx),
	}

	portraitM := models.Portrait{}
	total, err := portraitM.CountByCond(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	portraitList, err := portraitM.GetList(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var userIdArr []int64
	for _, portrait := range portraitList {
		userIdArr = append(userIdArr, int64(portrait.UserId))
	}
	userMap := getUserMap(ctx, userIdArr)

	// 数据组装
	var items []interface{}
	for _, portrait := range portraitList {
		tmp := portrait.SwapData()
		tmp["user_remark"] = ""
		if user, ok := userMap[int(portrait.UserId)]; ok {
			tmp["user_remark"] = user.Remark
		}

		items = append(items, tmp)
	}

	result := map[string]interface{}{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

type commentListParams struct {
	Limit         int            `json:"limit"`
	Page          int            `json:"page"`
	TaskTypeList  []def.EnumType `json:"task_type_list"`
	MinCreateTime int64          `json:"min_time"`
	MaxCreateTime int64          `json:"max_time"`
	Keyword       string         `json:"keyword"`
	TypeList      []def.EnumType `json:"type_list"`
	MinScore      float64        `json:"min_score"`
	MaxScore      float64        `json:"max_score"`
}

func ListComment(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params commentListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	minCreateTime := params.MinCreateTime
	maxCreateTime := params.MaxCreateTime
	keyword := helpers.Trim(params.Keyword)
	typeList := params.TypeList
	minScore := params.MinScore
	maxScore := params.MaxScore
	taskTypeList := params.TaskTypeList
	if maxCreateTime > 0 {
		maxCreateTime = maxCreateTime + 86400
	}

	// 参数check
	limit, page := formatPaging(params.Limit, params.Page)

	// 获取信息
	cond := models.CommentCond{
		Keyword:          keyword,
		GtCreatedAt:      helpers.UnixToTimeStr(minCreateTime),
		LtCreatedAt:      helpers.UnixToTimeStr(maxCreateTime),
		MinScore:         minScore,
		MaxScore:         maxScore,
		TypeList:         typeList,
		TaskTypeList:     taskTypeList,
		FilterUserIdList: getFilterUserId(ctx),
	}

	commentM := models.Comment{}
	total, err := commentM.CountByCond(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	commentList, err := commentM.GetList(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var userIdArr []int64
	for _, comment := range commentList {
		userIdArr = append(userIdArr, int64(comment.UserId))
	}
	userMap := getUserMap(ctx, userIdArr)

	// 数据组装
	var items []interface{}
	for _, comment := range commentList {
		tmp := comment.SwapData()
		tmp["user_remark"] = ""
		if user, ok := userMap[int(comment.UserId)]; ok {
			tmp["user_remark"] = user.Remark
		}

		items = append(items, tmp)
	}

	result := map[string]interface{}{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}
