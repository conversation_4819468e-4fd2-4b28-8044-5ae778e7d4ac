package controllers

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"icode.baidu.com/helix_web/services"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

// 获取任务信息
type billListParams struct {
	Limit       def.IntType       `json:"limit"`
	Page        def.IntType       `json:"page"`
	TaskType    []def.EnumType    `json:"task_type"`
	KeyId       def.AutoIDType    `json:"key_id"`
	CostType    def.EnumType      `json:"cost_type"`
	MinTaskTime def.TimestampType `json:"min_task_time"`
	MaxTaskTime def.TimestampType `json:"max_task_time"`
}

func ListBill(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params billListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)

	taskTypeList := params.TaskType
	keyId := params.KeyId
	costType := params.CostType
	minTaskTime := params.MinTaskTime
	maxTaskTime := params.MaxTaskTime
	if maxTaskTime > 0 {
		maxTaskTime = maxTaskTime + 86400
	}

	// 参数check
	limit, page := formatPaging(params.Limit, params.Page)

	// 获取任务信息
	cond := models.BillCond{
		TaskTypeList:     taskTypeList,
		KeyId:            keyId,
		CostType:         costType,
		GtCreatedAt:      helpers.UnixToTimeStr(minTaskTime),
		LtCreatedAt:      helpers.UnixToTimeStr(maxTaskTime),
		FilterUserIdList: getFilterUserId(ctx),
	}

	billM := models.Bill{}
	total, err := billM.CountByCond(ctx, cond, false)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	sumRes, err := billM.Sum(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	billList, err := billM.GetByCond(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	totalUser, err := billM.CountByCond(ctx, cond, true)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	var userIdList []int64
	var taskIdList []int64
	for _, bill := range billList {
		userIdList = append(userIdList, int64(bill.UserId))
		taskIdList = append(taskIdList, int64(bill.TaskId))
	}
	userMap := getUserMap(ctx, userIdList)
	taskMap := getTaskMap(ctx, taskIdList)

	// 数据组装
	var items []interface{}
	for _, bill := range billList {
		tmp := bill.SwapData()
		tmp["username"] = ""
		tmp["user_type"] = 0
		tmp["user_remark"] = ""
		if user, ok := userMap[int(bill.UserId)]; ok {
			tmp["username"] = user.Username
			tmp["user_type"] = user.Type
			tmp["user_remark"] = user.Remark
		}

		tmp["task_name"] = ""
		if task, ok := taskMap[int(bill.TaskId)]; ok {
			tmp["task_name"] = task.Name
		}
		items = append(items, tmp)
	}

	result := map[string]interface{}{
		"items":      items,
		"total":      total,
		"total_user": totalUser,
		"total_cost": helpers.FormatFloat(sumRes.Num, 2),
	}
	return helpers.SuccReturn(result)
}

// 获取用户账单
type userBillParams struct {
	Limit         int    `json:"limit"`
	Page          int    `json:"page"`
	MinTime       int    `json:"min_time"`
	MaxTime       int    `json:"max_time"`
	ProductIdList []int  `json:"product_id_list"`
	Keyword       string `json:"keyword"`
}

type billResp struct {
	Data billData `json:"data"`
	Code int      `json:"code"`
}
type billData struct {
	Total      int        `json:"total"`
	TotalUser  int        `json:"total_user"`
	CouponSum  int        `json:"coupon_sum"`
	CashSum    int        `json:"cash_sum"`
	ConsumeSum int        `json:"consume_sum"`
	Items      []billItem `json:"items"`
}
type billItem struct {
	Date          int    `json:"date"`
	Username      string `json:"username"`
	UserRemark    string `json:"user_remark"`
	AccountId     int    `json:"account_id"`
	ProductId     int    `json:"product_id"`
	Cash          int    `json:"cash"`
	CouponConsume int    `json:"coupon_consume"`
	Consume       int    `json:"consumption"`
}

func GetUserBill(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params userBillParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit := params.Limit
	page := params.Page

	minTime := params.MinTime
	maxTime := params.MaxTime
	productIdList := params.ProductIdList
	keyword := helpers.Trim(params.Keyword)
	if maxTime > 0 {
		maxTime = maxTime + 86400
	}

	// 参数check
	limit, page = formatPaging(limit, page)

	userM := models.User{}
	var accountIdList []int64
	if len(keyword) > 0 {
		userCond := models.UserCond{
			Keyword: []string{keyword},
		}
		userList, _ := userM.GetList(ctx, userCond, 1000, 1)

		for _, user := range userList {
			accountIdList = append(accountIdList, int64(user.RealID))
		}
	}

	// curl 请求
	var billResult billResp
	reqUrl := "http://10.12.72.128:8810/user/bill/get"

	// 发送内容
	content := map[string]interface{}{
		"token":           "user_bill_helix_token",
		"account_id_list": accountIdList,
		"api_id_list":     productIdList,
		"min_time":        minTime,
		"max_time":        maxTime,
		"page":            page,
		"limit":           limit,
	}
	contentByte, _ := json.Marshal(content)
	reqBody := strings.NewReader(string(contentByte))
	resp, err := http.Post(reqUrl, "application/json;charset=UTF-8", reqBody)
	if err != nil {
		return helpers.FailReturn(helpers.ServiceErrorCode, "call bill service error")
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error")
	}
	_ = json.Unmarshal(body, &billResult)

	// 数据组装
	var realIdList []int64
	for _, bill := range billResult.Data.Items {
		realIdList = append(realIdList, int64(bill.AccountId))
	}
	userMap := getUserMapByRealIds(ctx, realIdList)

	var items []interface{}
	for _, bill := range billResult.Data.Items {
		if user, ok := userMap[bill.AccountId]; ok {
			bill.Username = user.Username
			bill.UserRemark = user.Remark
		}

		items = append(items, bill)
	}

	result := map[string]interface{}{
		"total":       billResult.Data.Total,
		"total_user":  billResult.Data.TotalUser,
		"cash_sum":    billResult.Data.CashSum,
		"coupon_sum":  billResult.Data.CouponSum,
		"consume_sum": billResult.Data.ConsumeSum,
		"items":       items,
	}
	return helpers.SuccReturn(result)
}

type PushBillingReq struct {
	UserID   int64 `json:"user_id"`
	TaskType int   `json:"task_type"`
	Level    int   `json:"level"`
	Number   int64 `json:"number"`
}

func PushBilling(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params PushBillingReq
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	if params.UserID <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "invalid user id")
	}
	if params.TaskType <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "invalid task type")
	}
	if params.Level <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "invalid level")
	}
	if params.Number <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "invalid number")
	}

	taskConfig := models.PushBillingTaskConfig{
		Level:  params.Level,
		Number: params.Number,
	}
	taskConfigBytes, _ := json.Marshal(taskConfig)
	taskN := models.Task{
		UserId:    params.UserID,          // userID 为 helix_user表ID // 需要修改
		Type:      int32(params.TaskType), // 根据页面模块的计费项进行设置, serial/list 接口的 type 参数就是 // 需要修改
		Name:      "push-billing-task",    // 随意命名, 不改也行, 此参数入到 helix_task 表的name字段里
		FuncType:  20,                     // 可以找PM确认或者 通过helix_task表同类型任务来确定FuncType // 需要修改
		Config:    string(taskConfigBytes),
		ChargeTab: 11,
		IsExample: 0,
	}
	taskData, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, err.Error())
	}

	now := time.Now().Unix() - 300
	taskData.Status = def.EnumType(30)
	taskData.Result = ""
	taskData.FinishTime = time.Unix(now, 0)
	taskData.Resource = "CPU"
	taskData.ChargeStartTime = time.Unix(now-60*60, 0)
	taskData.ChargeEndTime = time.Unix(now, 0)
	err = taskData.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, err.Error())
	}

	services.DoCharge(ctx, taskData)
	taskData.Status = models.StatusDel
	taskData.ChargeTab = def.EnumType(models.ChargeTabFinish)
	_ = taskData.Save(ctx)

	return nil
}
