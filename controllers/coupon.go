package controllers

import (
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

// 优惠券添加
type couponAddParams struct {
	UserId    int64        `json:"user_id" validate:"required"`
	TaskType  def.EnumType `json:"task_type"  validate:"required"`
	Amount    float64      `json:"amount" validate:"required"`
	RangeList []int64      `json:"range_list" validate:"required"`
	StartTime int64        `json:"start_time" validate:"required"`
	EndTime   int64        `json:"end_time" validate:"required"`
	Remark    string       `json:"remark"`
}

func AddCoupon(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params couponAddParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)

	userId := params.UserId
	taskType := params.TaskType
	rangeList := params.RangeList
	amount := params.Amount
	startTime := params.StartTime
	endTime := params.EndTime
	remark := helpers.Trim(params.Remark)

	// 参数检验
	if !checkChargeTaskType(taskType) && taskType != models.TaskTypeHelixVS {
		return helpers.FailReturn(helpers.ParamErrorCode, "task_type params error")
	}
	if len(remark) > 512 || len(rangeList) <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "coupon params error")
	}
	if amount <= 0 || startTime < time.Now().Unix()-86400 || endTime < time.Now().Unix() {
		return helpers.FailReturn(helpers.ParamErrorCode, "coupon params error")
	}

	// 用户check
	userM := models.User{}
	userInfo, err := userM.GetUserById(ctx, userId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	if userInfo.ID <= 0 || userInfo.Type == uint64(models.UserTypeNormal) {
		return helpers.FailReturn(helpers.ParamErrorCode, "user 不支持添加")
	}

	// 获取同类型优惠券信息
	couponM := models.Coupon{}
	couponList, err := couponM.GetListByTaskType(ctx, userId, taskType)
	for _, coupon := range couponList {
		coupon.Status = models.CouponStatusLose
		_ = coupon.Save(ctx)
	}

	// 创建优惠券记录
	rangeListByte, _ := json.Marshal(rangeList)
	couponNew := models.Coupon{
		AdminId:    getAdminId(ctx),
		UserId:     def.AutoIDType(userInfo.ID),
		TaskType:   taskType,
		RangeList:  string(rangeListByte),
		Amount:     amount,
		RestAmount: amount,
		StartTime:  helpers.UnixToTime(startTime),
		EndTime:    helpers.UnixToTime(endTime + 86400),
		Remark:     remark,
	}
	_, err = couponNew.Add(ctx, couponNew)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}

// 设置试用记录
type couponSetParams struct {
	CouponId int64  `json:"coupon_id"`
	Operate  string `json:"operate_type"`
}

func SetCoupon(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params couponSetParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	operate := helpers.Trim(params.Operate)
	if operate != "del" && operate != "stop" {
		return helpers.FailReturn(helpers.ParamErrorCode, "operate_type params error")
	}

	couponM := models.Coupon{}
	couponInfo, err := couponM.GetById(ctx, params.CouponId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	if couponInfo.ID <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "trial record empty")
	}

	couponInfo.AdminId = getAdminId(ctx)
	couponInfo.Status = models.StatusDel
	if operate == "stop" {
		couponInfo.Status = models.TrialStatusLose
		couponInfo.EndTime = time.Now()
	}
	_ = couponInfo.Save(ctx)

	return helpers.SuccReturn(nil)
}
