package controllers

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

type skillListParams struct {
	Type def.EnumType `json:"type"`
}

func ListSkill(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params skillListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)

	skillM := models.Skill{}
	skillList, err := skillM.GetByType(ctx, params.Type)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var items []any
	for _, skill := range skillList {
		items = append(items, skill.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(result)
}

type skillAddParams struct {
	Type def.EnumType   `json:"type"`
	Sort int64          `json:"sort"`
	Data map[string]any `json:"data"`
}

func AddSkill(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params skillAddParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	if !checkSkillType(params.Type) || len(params.Data) <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "param error")
	}

	dataByte, _ := json.Marshal(params.Data)
	skillNew := models.Skill{
		AdminId: getAdminId(ctx),
		Type:    params.Type,
		Sort:    params.Sort,
		Data:    string(dataByte),
	}
	_, err := skillNew.Add(ctx, skillNew)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	return helpers.SuccReturn(nil)
}

type skillUpdateParams struct {
	Id   int64          `json:"id"`
	Sort int64          `json:"sort"`
	Data map[string]any `json:"data"`
}

func UpdateSkill(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params skillUpdateParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	if params.Id < 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id param error")
	}
	if params.Sort < 0 || len(params.Data) <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "param error")
	}

	skillM := models.Skill{}
	skillInfo, err := skillM.GetById(ctx, params.Id)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if skillInfo.ID > 0 {
		dataByte, _ := json.Marshal(params.Data)
		skillInfo.Sort = params.Sort
		skillInfo.Data = string(dataByte)
		_ = skillInfo.Save(ctx)
	}
	return helpers.SuccReturn(nil)
}

type skillSetParams struct {
	Id          int64  `json:"id"`
	OperateType string `json:"operate_type"`
}

func SetSkill(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params skillSetParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	operateType := helpers.Trim(params.OperateType)
	if !checkOperateType(operateType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "operate_type params error")
	}

	skillM := models.Skill{}
	skillInfo, err := skillM.GetById(ctx, params.Id)
	if err != nil || skillInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	switch operateType {
	case OperateTypePublish:
		skillInfo.Status = models.StatusOnline
		break
	case OperateTypeWithdraw:
		skillInfo.Status = models.StatusNew
		break
	}
	err = skillInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	return helpers.SuccReturn(nil)
}

func checkSkillType(skillType def.EnumType) bool {
	res, _ := models.SkillTypeMap[skillType]
	return res
}
