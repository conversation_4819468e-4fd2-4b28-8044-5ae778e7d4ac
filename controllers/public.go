package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"icode.baidu.com/helix_web/library/bceaa"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
)

const (
	TokenSalt = "helix_salt"
)

// 管理后台登陆
type loginParams struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

func Login(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params loginParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	username := params.Username
	password := params.Password

	// 验证逻辑处理
	adminM := models.Admin{}
	adminData, err := adminM.GetByUsername(ctx, username)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if adminData.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "username not exist")
	}

	check := adminM.CheckPassword(password, adminData.Password)
	if !check {
		return helpers.FailReturn(helpers.LogicErrorCode, "password error")
	}

	// 生产token、并写入redis
	token := helpers.Md5(strconv.Itoa(int(adminData.ID)) + TokenSalt)
	cacheKey := redis.AdminTokenPrefix + token
	redis.Set(ctx, cacheKey, adminData.ID, redis.AdminTokenLimitTime)

	result := map[string]any{
		"token": token,
	}
	return helpers.SuccReturn(result)
}

type getBosAuthParams struct {
	Type int `json:"type"`
}

// 获取 bos 临时信息
func GetBosAuth(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params getBosAuthParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	bosType := params.Type

	var err error
	var resMap map[string]interface{}
	switch bosType {
	case 1:
		resMap, err = bceaa.GetSessionToken()
	default:
		resMap, err = bce.GetSessionToken()
	}
	if err != nil {
		go helpers.HelixNotice(ctx, "----- bos error ---"+err.Error())
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	return helpers.SuccReturn(resMap)
}

// 获取 bos 访问链接
// 生产有时间限制的 bos url
type bosUrlParams struct {
	BosList []string `json:"bos_list"`
	Type    int      `json:"type"`
}

func GetBosUrl(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params bosUrlParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	bosType := params.Type
	bosList := params.BosList
	if len(bosList) <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "bos_list params error")
	}

	var result []string
	for _, bosPath := range bosList {
		bucket, object := helpers.DealBosFileUrl(bosPath)
		var err error
		var bosUrl string
		switch bosType {
		case 1:
			bosUrl, err = bceaa.GenerateObjectURL(bucket, object, bceaa.ObjectURLExpireTime)
		default:
			bosUrl, err = bce.GenerateObjectUrl(bucket, object, bce.ObjectUrlExpireTime)
		}
		if err != nil {
			go helpers.HelixNotice(ctx, "--- bos GenerateObject err ----"+err.Error())
		}
		result = append(result, bosUrl)
	}

	return helpers.SuccReturn(result)
}

// 权限初始化
func PermissionInit() {
	ctx := context.Background()

	// 添加权限数据
	permissionM := models.Permission{}
	raw, _ := permissionM.GetList(ctx)
	if len(raw) > 0 {
		fmt.Println("----done----")
		return
	}

	permList := map[int][]string{
		1:  {"/role/list", "角色列表"},
		2:  {"/admin/list", "管理员列表"},
		3:  {"/admin/add", "管理员添加"},
		4:  {"/admin/update", "管理员修改"},
		5:  {"/admin/del", "管理员删除"},
		6:  {"/admin/info", "获取管理员信息"},
		7:  {"/admin/menu", "管理员菜单"},
		8:  {"/password/update", "修改密码"},
		9:  {"/feedbar/list", "动态栏列表"},
		10: {"/feedbar/add", "动态栏添加"},
		11: {"/feedbar/update", "动态栏修改"},
		12: {"/feedbar/set", "动态栏设置"},
		13: {"/achievement/list", "学术成果列表"},
		14: {"/achievement/add", "学术成果添加"},
		15: {"/achievement/update", "学术成果修改"},
		16: {"/achievement/set", "学术成果设置"},
		17: {"/example/list", "示例列表"},
		18: {"/example/add", "示例添加"},
		19: {"/example/update", "示例修改"},
		20: {"/example/set", "示例设置"},
		21: {"/dataset/statistics", "数据总览"},
		22: {"/dataset/list", "数据集列表"},
		23: {"/dataset/info", "数据集详情"},
		24: {"/dataset/history", "历史版本"},
		25: {"/dataset/collect", "数据集收藏"},
		26: {"/dataset/add", "添加数据集"},
		27: {"/dataset/edit", "编辑数据集"},
		28: {"/dataset/log", "数据集操作日志"},
		29: {"/dataset/url", "下载链接"},
		30: {"/tag/add", "添加标签"},
		31: {"/tag/edit", "编辑标签"},
		32: {"/tag/list", "标签列表"},
		33: {"/tag/del", "删除标签"},
	}

	var totalDatasetIdList, logDatasetIdList, datasetIdList, tagIdList []int64
	var allIdList, superIdList, commonIdList, feedIdList, achievementIdList, exampleIdList []int64
	for i := 1; i <= 33; i++ {
		val := permList[i]
		permNew := models.Permission{
			Uri:  val[0],
			Name: val[1],
		}
		permInfo, _ := permissionM.Add(context.Background(), permNew)

		// id 处理
		allIdList = append(allIdList, int64(permInfo.ID))
		if val[0] == "/role/list" || val[0] == "/admin/list" || val[0] == "/admin/add" ||
			val[0] == "/admin/update" || val[0] == "/admin/del" {
			superIdList = append(superIdList, int64(permInfo.ID))
		}
		if val[0] == "/admin/info" || val[0] == "/admin/menu" || val[0] == "/password/update" {
			commonIdList = append(commonIdList, int64(permInfo.ID))
		}
		if strings.Contains(val[0], "feedbar") {
			feedIdList = append(feedIdList, int64(permInfo.ID))
		}
		if strings.Contains(val[0], "achievement") {
			achievementIdList = append(achievementIdList, int64(permInfo.ID))
		}
		if strings.Contains(val[0], "example") {
			exampleIdList = append(exampleIdList, int64(permInfo.ID))
		}

		if val[0] == "/dataset/statistics" {
			totalDatasetIdList = append(totalDatasetIdList, int64(permInfo.ID))
		}
		if val[0] == "/dataset/log" {
			logDatasetIdList = append(logDatasetIdList, int64(permInfo.ID))
		}
		if strings.Contains(val[0], "dataset") && val[0] != "/dataset/statistics" && val[0] != "/dataset/log" {
			datasetIdList = append(datasetIdList, int64(permInfo.ID))
		}
		if strings.Contains(val[0], "tag") {
			tagIdList = append(tagIdList, int64(permInfo.ID))
		}
	}

	// 菜单
	tmpIdList := append(append(feedIdList, achievementIdList...), exampleIdList...)
	tmpIdByte, _ := json.Marshal(tmpIdList)
	menuNew := models.Menu{
		Name:          "展示管理",
		PermissionIds: string(tmpIdByte),
		ParentId:      0,
	}
	menuInfo1, _ := menuNew.Add(context.Background(), menuNew)

	tmpIdByte, _ = json.Marshal(superIdList)
	menuNew = models.Menu{
		Name:          "后台管理",
		PermissionIds: string(tmpIdByte),
		ParentId:      0,
	}
	menuInfo2, _ := menuNew.Add(context.Background(), menuNew)

	tmpIdList = append(datasetIdList, tagIdList...)
	tmpIdByte, _ = json.Marshal(tmpIdList)
	menuNew = models.Menu{
		Name:          "数据库管理",
		PermissionIds: string(tmpIdByte),
		ParentId:      0,
	}
	menuInfo3, _ := menuNew.Add(context.Background(), menuNew)

	norMenuIdList := []int64{int64(menuInfo1.ID)}
	dataMenuIdList := []int64{int64(menuInfo3.ID)}
	allMenuIdList := []int64{int64(menuInfo1.ID), int64(menuInfo2.ID), int64(menuInfo3.ID)}
	var tagMenuIdList []int64

	menuList := []string{
		"首页动态栏",
		"学术成果",
		"示例管理",
		"安全管理",
		"数据集统计",
		"数据集列表",
		"操作记录",
		"标签管理",
	}
	for _, val := range menuList {
		menuNew := models.Menu{
			Name:     val,
			ParentId: 0,
		}
		var menuInfo models.Menu
		switch val {
		case "首页动态栏":
			tmpIdByte, _ := json.Marshal(feedIdList)

			menuNew.ParentId = menuInfo1.ID
			menuNew.PermissionIds = string(tmpIdByte)
			menuInfo, _ = menuNew.Add(ctx, menuNew)
			norMenuIdList = append(norMenuIdList, int64(menuInfo.ID))
			break
		case "学术成果":
			tmpIdByte, _ := json.Marshal(achievementIdList)

			menuNew.ParentId = menuInfo1.ID
			menuNew.PermissionIds = string(tmpIdByte)
			menuInfo, _ = menuNew.Add(ctx, menuNew)
			norMenuIdList = append(norMenuIdList, int64(menuInfo.ID))
			break
		case "示例管理":
			tmpIdByte, _ := json.Marshal(exampleIdList)

			menuNew.ParentId = menuInfo1.ID
			menuNew.PermissionIds = string(tmpIdByte)
			menuInfo, _ = menuNew.Add(ctx, menuNew)
			norMenuIdList = append(norMenuIdList, int64(menuInfo.ID))
			break
		case "安全管理":
			tmpIdByte, _ := json.Marshal(superIdList)

			menuNew.PermissionIds = string(tmpIdByte)
			menuNew.ParentId = menuInfo2.ID
			menuInfo, _ = menuNew.Add(ctx, menuNew)
			break
		case "数据集统计":
			tmpIdByte, _ := json.Marshal(totalDatasetIdList)

			menuNew.PermissionIds = string(tmpIdByte)
			menuNew.ParentId = menuInfo3.ID
			menuInfo, _ = menuNew.Add(ctx, menuNew)
			dataMenuIdList = append(dataMenuIdList, int64(menuInfo.ID))
			break
		case "数据集列表":
			tmpIdByte, _ := json.Marshal(datasetIdList)

			menuNew.PermissionIds = string(tmpIdByte)
			menuNew.ParentId = menuInfo3.ID
			menuInfo, _ = menuNew.Add(ctx, menuNew)
			dataMenuIdList = append(dataMenuIdList, int64(menuInfo.ID))
			break
		case "操作记录":
			tmpIdByte, _ := json.Marshal(logDatasetIdList)

			menuNew.PermissionIds = string(tmpIdByte)
			menuNew.ParentId = menuInfo3.ID
			menuInfo, _ = menuNew.Add(ctx, menuNew)
			dataMenuIdList = append(dataMenuIdList, int64(menuInfo.ID))
			break
		case "标签管理":
			tmpIdByte, _ := json.Marshal(tagIdList)

			menuNew.PermissionIds = string(tmpIdByte)
			menuNew.ParentId = menuInfo3.ID
			menuInfo, _ = menuNew.Add(ctx, menuNew)
			tagMenuIdList = append(tagMenuIdList, int64(menuInfo.ID))
			break
		}
		allMenuIdList = append(allMenuIdList, int64(menuInfo.ID))
	}

	// 角色
	roleList := map[string]string{
		"super":     "超级管理员",
		"normal":    "平台管理员",
		"db_manage": "数据库管理员",
		"db":        "数据库成员",
	}

	var superRoleId int64
	for key, role := range roleList {
		switch key {
		case "super":
			tmpIdByte, _ := json.Marshal(allMenuIdList)

			roleNew := models.Role{
				Name:    role,
				Alias:   models.AliasSuper,
				MenuIds: string(tmpIdByte),
			}
			roleInfo, _ := roleNew.Add(ctx, roleNew)
			superRoleId = roleInfo.ID
			break
		case "normal":
			tmpIdByte, _ := json.Marshal(norMenuIdList)

			roleNew := models.Role{
				Name:    role,
				Alias:   models.AliasPlatform,
				MenuIds: string(tmpIdByte),
			}
			roleNew.Add(ctx, roleNew)
			break
		case "db_manage":
			tmpIdList := append(dataMenuIdList, tagMenuIdList...)
			tmpIdByte, _ := json.Marshal(tmpIdList)

			roleNew := models.Role{
				Name:    role,
				Alias:   models.AliasDBManage,
				MenuIds: string(tmpIdByte),
			}
			roleNew.Add(ctx, roleNew)
			break
		case "db":
			tmpIdByte, _ := json.Marshal(dataMenuIdList)

			roleNew := models.Role{
				Name:    role,
				Alias:   models.AliasDB,
				MenuIds: string(tmpIdByte),
			}
			roleNew.Add(ctx, roleNew)
			break
		}
	}

	// admin
	tmpIdByte, _ = json.Marshal(allIdList)

	adminM := models.Admin{}
	adminNew := models.Admin{
		Username:      "helix001",
		Password:      adminM.EncryptPassword("helix001"),
		RoleId:        superRoleId,
		PermissionIds: string(tmpIdByte),
	}
	adminNew.Add(ctx, adminNew)

	fmt.Println("-----done----")
}
