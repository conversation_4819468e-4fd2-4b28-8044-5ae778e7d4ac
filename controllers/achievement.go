package controllers

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

type achievementListParams struct {
	Limit def.IntType `json:"limit"`
	Page  def.IntType `json:"page"`
}

func GetAchievementList(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params achievementListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit, page := formatPaging(params.Limit, params.Page)

	achievementM := models.Achievement{}
	total, err := achievementM.CountByCond(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	achievementList, err := achievementM.GetList(ctx, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据组装
	var adminIdList []int64
	for _, achievement := range achievementList {
		adminIdList = append(adminIdList, int64(achievement.AdminId))
	}
	adminMap := getAdminMap(ctx, adminIdList)

	var items []interface{}
	for _, achievement := range achievementList {
		tmp := achievement.SwapData()

		tmp["admin_name"] = ""
		if _, ok := adminMap[int(achievement.AdminId)]; ok {
			tmp["admin_name"] = adminMap[int(achievement.AdminId)]
		}
		items = append(items, tmp)
	}

	result := map[string]interface{}{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 添加
type addAchievementParams struct {
	PublishTime def.TimestampType `json:"publish_time" validate:"required"`
	Title       def.TrimString    `json:"title" validate:"required,min=1,max=512"`
	Url         def.TrimString    `json:"url" validate:"required,min=1,max=256"`
	Author      def.TrimString    `json:"author" validate:"required,min=1,max=512"`
	TagList     []def.TrimString  `json:"tag_list"`
	TagListEn   []def.TrimString  `json:"tag_list_en"`
}

func AddAchievement(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addAchievementParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	//err := resource.Validator.Struct(params)
	//if err != nil {
	//	return helpers.FailReturn(helpers.LogicErrorCode, helpers.Validate(err))
	//}

	if len(params.TagList) > 20 || len(params.TagListEn) > 20 {
		return helpers.FailReturn(helpers.LogicErrorCode, "tag_list params error")
	}
	tagListByte, _ := json.Marshal(params.TagList)
	tagListEnByte, _ := json.Marshal(params.TagListEn)

	// 添加数据
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	achievementM := models.Achievement{}
	achievementNew := models.Achievement{
		PublishTime: helpers.UnixToTime(params.PublishTime),
		Title:       string(params.Title),
		Author:      string(params.Author),
		Url:         string(params.Url),
		TagList:     string(tagListByte),
		TagListEn:   string(tagListEnByte),
		AdminId:     adminInfo.AdminId,
	}
	_, err := achievementM.Add(ctx, achievementNew)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	return helpers.SuccReturn(nil)
}

// 更新学术成果
type updateAchievementParams struct {
	Id          def.AutoIDType    `json:"id" validate:"required,min=1"`
	PublishTime def.TimestampType `json:"publish_time" validate:"required"`
	Title       def.TrimString    `json:"title" validate:"required,min=1,max=512"`
	Url         def.TrimString    `json:"url" validate:"required,min=1,max=256"`
	Author      def.TrimString    `json:"author" validate:"required,min=1,max=512"`
	TagList     []def.TrimString  `json:"tag_list"`
	TagListEn   []def.TrimString  `json:"tag_list_en"`
}

func UpdateAchievement(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params updateAchievementParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	//err := resource.Validator.Struct(params)
	//if err != nil {
	//	return helpers.FailReturn(helpers.ParamErrorCode, helpers.Validate(err))
	//}

	if len(params.TagList) > 20 || len(params.TagListEn) > 20 {
		return helpers.FailReturn(helpers.LogicErrorCode, "tag_list params error")
	}
	tagListByte, _ := json.Marshal(params.TagList)
	tagListEnByte, _ := json.Marshal(params.TagListEn)

	// 添加数据
	achievementM := models.Achievement{}
	achievementInfo, err := achievementM.GetById(ctx, params.Id)
	if err != nil || achievementInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	achievementInfo.PublishTime = helpers.UnixToTime(params.PublishTime)
	achievementInfo.Title = string(params.Title)
	achievementInfo.Url = string(params.Url)
	achievementInfo.Author = string(params.Author)
	achievementInfo.TagList = string(tagListByte)
	achievementInfo.TagListEn = string(tagListEnByte)
	err = achievementInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}

// 设置动态栏
type setAchievementParams struct {
	Id          def.AutoIDType `json:"id" validate:"required,min=1"`
	OperateType def.TrimString `json:"operate_type" validate:"required"`
}

func SetAchievement(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params setAchievementParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	//err := resource.Validator.Struct(params)
	//if err != nil {
	//	return helpers.FailReturn(helpers.ParamErrorCode, helpers.Validate(err))
	//}

	// check
	if !checkOperateType(string(params.OperateType)) {
		return helpers.FailReturn(helpers.ParamErrorCode, "operate_type params error")
	}

	achievementM := models.Achievement{}
	achievementInfo, err := achievementM.GetById(ctx, params.Id)
	if err != nil || achievementInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	switch params.OperateType {
	case OperateTypePublish:
		achievementInfo.Status = models.StatusOnline
		break
	case OperateTypeWithdraw:
		achievementInfo.Status = models.StatusNew
		break
	}
	err = achievementInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}
