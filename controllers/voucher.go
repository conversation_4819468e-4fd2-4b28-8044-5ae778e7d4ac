package controllers

import (
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

type voucherAddParams struct {
	UserId    int64        `json:"user_id" validate:"required"`
	TaskType  def.EnumType `json:"task_type"  validate:"required"`
	Amount    float64      `json:"amount" validate:"required"`
	StartTime int64        `json:"start_time" validate:"required"`
	EndTime   int64        `json:"end_time" validate:"required"`
	Remark    string       `json:"remark"`
}

func AddVoucher(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params voucherAddParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)

	// 参数检验
	remark := helpers.Trim(params.Remark)
	if len(remark) > 512 || params.Amount <= 0 || params.StartTime < time.Now().Unix()-86400 ||
		params.EndTime < time.Now().Unix() {
		return helpers.FailReturn(helpers.ParamErrorCode, "param error")
	}

	// 用户check
	userM := models.User{}
	userInfo, err := userM.GetUserById(ctx, params.UserId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	if userInfo.ID <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "用户不存在")
	}

	// 创建点券记录
	voucherNew := models.Voucher{
		AdminID:    getAdminId(ctx),
		UserID:     def.AutoIDType(userInfo.ID),
		TaskType:   int(params.TaskType),
		Amount:     params.Amount,
		RestAmount: params.Amount,
		StartTime:  helpers.UnixToTime(params.StartTime),
		EndTime:    helpers.UnixToTime(params.EndTime + 86400),
		Remark:     remark,
		Status:     models.VoucherStatusNormal,
	}
	if err = voucherNew.Add(ctx); err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}

// 修改点券
type voucherSetParams struct {
	VoucherID int64  `json:"voucher_id"`
	Operate   string `json:"operate"`
}

func SetVoucher(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params voucherSetParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	operate := helpers.Trim(params.Operate)
	if operate != "del" && operate != "stop" {
		return helpers.FailReturn(helpers.ParamErrorCode, "operate params error")
	}

	voucherM := models.Voucher{}
	voucherInfo, err := voucherM.GetByID(ctx, params.VoucherID)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	if voucherInfo.ID <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "voucher not found")
	}

	voucherInfo.AdminID = getAdminId(ctx)
	switch operate {
	case "del":
		voucherInfo.Status = models.VoucherStatusDeleted
	case "stop":
		voucherInfo.Status = models.VoucherStatusDisabled
		voucherInfo.EndTime = time.Now()
	}
	_ = voucherInfo.Save(ctx)

	return helpers.SuccReturn(nil)
}
