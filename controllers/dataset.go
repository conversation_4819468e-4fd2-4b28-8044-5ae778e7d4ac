package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

const MonthNum def.IntType = 6

// 数据集总览页
func GetDatasetStatistics(ctx context.Context, req ghttp.Request) ghttp.Response {
	datasetM := models.Dataset{}

	// 总数统计
	var cond models.DatasetCond
	totalRes, err := datasetM.Sum(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	nowTime := time.Now()
	var items []map[string]any
	for i := MonthNum; i > 0; i-- {
		dateEnd := nowTime.AddDate(0, -i+1, 0).Format("2006-01")
		cond.LtCreatedAt = dateEnd + "-01"

		sumRes, err := datasetM.Sum(ctx, cond)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}

		formatTime, _ := time.ParseInLocation("2006-01-02", cond.LtCreatedAt, time.Local)
		item := map[string]any{
			"date": formatTime.Unix(),
			"size": sumRes.Num,
		}
		items = append(items, item)
	}
	overview := map[string]any{
		"total_size": totalRes.Num,
		"items":      items,
	}

	// 分类统计
	var cond2 models.DatasetCond
	resultList, err := datasetM.GroupSum(ctx, cond2, "source_type")
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var sourceData []map[string]any
	for _, val := range resultList {
		typeInt, _ := strconv.Atoi(val.Field)
		item := map[string]any{
			"type":  typeInt,
			"size":  val.Num,
			"count": val.Total,
		}
		sourceData = append(sourceData, item)
	}

	// 获取标签
	tagM := models.Tag{}
	cond3 := models.TagCond{
		ParentIdList: []int64{int64(models.DefaultParentId)},
	}
	tagList, err := tagM.GetList(ctx, cond3, 200, 1)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	tagMap := make(map[int64]models.Tag)
	for _, val := range tagList {
		tagMap[int64(val.ID)] = val
	}

	// 按标签统计
	var tagRelateCond models.TagRelateCond
	tagRelateM := models.DatasetTagRelate{}
	tagRelateList, err := tagRelateM.GetList(ctx, tagRelateCond, 20000)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 标签map
	tagIdDatasetMap := make(map[int64][]int64)
	for _, val := range tagRelateList {
		if item, ok := tagIdDatasetMap[int64(val.ParentId)]; ok {
			tagIdDatasetMap[int64(val.ParentId)] = append(item, int64(val.DatasetId))
		} else {
			tagIdDatasetMap[int64(val.ParentId)] = []int64{int64(val.DatasetId)}
		}
	}

	var objectData, dimensionData, purposeData []map[string]any
	for tagId, datasetIdList := range tagIdDatasetMap {
		if _, ok := tagMap[tagId]; !ok {
			continue
		}

		// 获取数据集统计（datasetIdList 在这里会去重）
		datasetCond := models.DatasetCond{
			IdList: datasetIdList,
		}
		sumRes, err := datasetM.Sum(ctx, datasetCond)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}

		item := map[string]any{
			"type":  tagMap[tagId].Type,
			"name":  tagMap[tagId].Name,
			"size":  sumRes.Num,
			"count": sumRes.Total,
		}

		switch tagMap[tagId].Type {
		case models.TagTypeObject:
			objectData = append(objectData, item)
			break
		case models.TagTypeDimension:
			dimensionData = append(dimensionData, item)
			break
		default:
			purposeData = append(purposeData, item)
			break
		}
	}

	result := map[string]any{
		"overview":       overview,
		"object_data":    objectData,
		"dimension_data": dimensionData,
		"purpose_data":   purposeData,
		"source_data":    sourceData,
	}
	return helpers.SuccReturn(result)
}

// 数据集详情
type datasetInfoParams struct {
	Id int64 `json:"id"`
}

func GetDatasetInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params datasetInfoParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id params error")
	}

	// 获取数据
	datasetM := models.Dataset{}
	datasetInfo, err := datasetM.GetById(ctx, params.Id)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if datasetInfo.ID <= 0 {
		return helpers.SuccReturn(nil)
	}

	// 获取最新版本
	dCond := models.DatasetCond{
		BaseId: int64(datasetInfo.BaseId),
	}
	datasetList, err := datasetM.GetList(ctx, dCond, 1, 1)
	if err != nil || len(datasetList) <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	nextVersion, _ := strconv.ParseFloat(datasetList[0].Version, 64)
	nextVersion = helpers.FormatFloat(nextVersion+0.1, 1)

	// tag map
	tagMap, err := getTagMap(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, err.Error())
	}

	// admin map
	adminName := ""
	adminMap := getAdminMap(ctx, []int64{int64(datasetInfo.AdminId)})
	if val, ok := adminMap[int(datasetInfo.AdminId)]; ok {
		adminName = val
	}

	// 返回数据
	result := datasetInfo.SwapData()
	result["object_data"] = getTagData(tagMap, datasetInfo.ObjectIds)
	result["dimension_data"] = getTagData(tagMap, datasetInfo.DimensionIds)
	result["purpose_data"] = getTagData(tagMap, datasetInfo.PurposeIds)
	result["latest_version"] = strconv.FormatFloat(nextVersion, 'f', -1, 64)
	result["admin_name"] = adminName
	return helpers.SuccReturn(result)
}

// 数据集历史版本
type collectParams struct {
	Id     int64 `json:"id"`
	IsLike bool  `json:"is_like"`
}

func CollectDataset(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params collectParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	id := params.Id
	isLike := params.IsLike

	// 获取数据
	datasetM := models.Dataset{}
	datasetInfo, err := datasetM.GetById(ctx, id)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	if datasetInfo.ID > 0 {
		like := models.IsLikeYes
		if !isLike {
			like = models.IsLikeNo
		}
		datasetInfo.IsLike = like

		err = datasetInfo.Save(ctx, "is_like")
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}
	}
	return helpers.SuccReturn(nil)
}

// 数据集历史版本
type datasetHistoryParams struct {
	Id int64 `json:"id"`
}

func GetDatasetHistory(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params datasetHistoryParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id params error")
	}

	// 获取数据
	datasetM := models.Dataset{}
	datasetInfo, err := datasetM.GetById(ctx, params.Id)
	if err != nil || datasetInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var items []any
	if datasetInfo.BaseId > 0 {
		datasetCond := models.DatasetCond{
			BaseId: int64(datasetInfo.BaseId),
		}
		datasetList, err := datasetM.GetList(ctx, datasetCond, 100, 1)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}

		// adminMap
		allAdminMap := getAllAdminMap(ctx)

		for _, item := range datasetList {
			tmp := item.SwapData()
			tmp["admin_name"] = ""
			if val, ok := allAdminMap[int(item.AdminId)]; ok {
				tmp["admin_name"] = val
			}

			items = append(items, tmp)
		}
	}

	// 返回数据
	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 数据集添加
type addDatasetParams struct {
	Id           int64          `json:"id"`
	BaseId       int64          `json:"base_id"`
	FileUrl      string         `json:"file_url"`
	Version      string         `json:"version"`
	VersionDesc  string         `json:"version_desc"`
	Name         string         `json:"name"`
	Desc         string         `json:"desc"`
	Remark       string         `json:"remark"`
	Size         float64        `json:"size"`
	Format       string         `json:"format"`
	Owner        string         `json:"owner"`
	ObjectIds    []int64        `json:"object_ids"`
	DimensionIds []int64        `json:"dimension_ids"`
	PurposeIds   []int64        `json:"purpose_ids"`
	MetricConfig []metricConfig `json:"metric_config"`
	SourceType   def.EnumType   `json:"source_type"`
	SourceConfig sourceConfig   `json:"source_config"`
}
type metricConfig struct {
	Metric   string `json:"metric"`
	Num      int64  `json:"num"`
	TagId    int64  `json:"tag_id"`
	ParentId int64  `json:"parent_id"`
	FullName string `json:"full_name"`
}
type sourceConfig struct {
	Name    string `json:"name"`
	Mark    string `json:"mark"`
	Url     string `json:"url"`
	Desc    string `json:"desc"`
	Partner string `json:"partner"`
}

func AddDataset(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addDatasetParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	baseId := params.BaseId
	fileUrl := helpers.Trim(params.FileUrl)
	version := helpers.Trim(params.Version)
	versionDesc := helpers.Trim(params.VersionDesc)
	name := helpers.Trim(params.Name)
	desc := helpers.Trim(params.Desc)
	remark := helpers.Trim(params.Remark)
	format := helpers.Trim(params.Format)
	owner := helpers.Trim(params.Owner)
	size := params.Size
	objectIdList := params.ObjectIds
	dimensionIdList := params.DimensionIds
	purposeIdList := params.PurposeIds
	metricConf := params.MetricConfig
	sourceType := params.SourceType
	sourceConf := params.SourceConfig

	fileUrlLen := len(fileUrl)
	if fileUrlLen <= 0 || fileUrlLen > UrlLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "file_url params error")
	}
	if len(format) <= 0 || len(format) > FormatLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "format params error")
	}
	if len(owner) <= 0 || len(owner) > OwnerLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "owner params error")
	}

	versionLen := len(version)
	if versionLen > VersionLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "version params error")
	}
	if len(versionDesc) > VersionDescLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "version_desc params error")
	}
	nameLen := len(name)
	if nameLen <= 0 || nameLen > NameLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "name params error")
	}
	if len(desc) > DatasetDescLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "desc params error")
	}
	if len(remark) > DatasetRemarkLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "remark params error")
	}
	if len(objectIdList) > TagIdNumLimit || len(dimensionIdList) > TagIdNumLimit || len(purposeIdList) > TagIdNumLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "tag select limit")
	}
	if !checkSourceType(sourceType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "source params error")
	}

	// check name
	datasetM := models.Dataset{}
	datasetInfo, err := datasetM.GetByName(ctx, name)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if datasetInfo.ID > 0 && datasetInfo.BaseId != baseId {
		return helpers.FailReturn(helpers.ParamErrorCode, "name repeat")
	}

	if baseId > 0 {
		datasetData, err := datasetM.GetById(ctx, baseId)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}
		if datasetData.ID <= 0 {
			return helpers.FailReturn(helpers.ParamErrorCode, "base_id params error")
		}

		// check version
		dCond := models.DatasetCond{
			Version: version,
			BaseId:  baseId,
		}
		count, err := datasetM.Count(ctx, dCond)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}
		if count > 0 {
			return helpers.FailReturn(helpers.ParamErrorCode, "version repeat")
		}
	}
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)

	// 写入数据
	objectIdByte, _ := json.Marshal(objectIdList)
	dimensionIdByte, _ := json.Marshal(dimensionIdList)
	purposeIdByte, _ := json.Marshal(purposeIdList)
	sourceConfByte, _ := json.Marshal(sourceConf)
	metricConfByte, _ := json.Marshal(metricConf)
	datasetNew := models.Dataset{
		AdminId:      adminInfo.AdminId,
		Name:         name,
		Version:      version,
		VersionDesc:  versionDesc,
		SourceType:   sourceType,
		Desc:         desc,
		Remark:       remark,
		FileUrl:      fileUrl,
		Format:       format,
		Owner:        owner,
		IsNew:        1,
		Size:         helpers.FormatFloat(size, 4),
		ObjectIds:    string(objectIdByte),
		DimensionIds: string(dimensionIdByte),
		PurposeIds:   string(purposeIdByte),
		SourceConfig: string(sourceConfByte),
		MetricConfig: string(metricConfByte),
	}
	datasetNew, err = datasetM.Add(ctx, datasetNew)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 修改baseId
	datasetNew.BaseId = datasetNew.ID
	if baseId > 0 {
		// 修改处理
		datasetCond := models.DatasetCond{
			BaseId: baseId,
		}
		_ = datasetM.UpdateByCond(ctx, datasetCond, map[string]any{"is_new": 0})

		// 变更
		datasetNew.BaseId = baseId
		datasetNew.IsNew = 1
	}
	datasetNew.Save(ctx)

	// 写入dataset relate
	tagIdList := append(append(objectIdList, dimensionIdList...), purposeIdList...)
	err = batchAddRelate(ctx, datasetNew.ID, tagIdList)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, err.Error())
	}

	// dataset log 写入
	operateType := models.OperateTypeAdd
	if baseId > 0 {
		operateType = models.OperateTypeVersionUpdate
	}
	addDatasetLog(ctx, adminInfo.AdminId, datasetNew.ID, operateType)

	return helpers.SuccReturn(nil)
}

// EditDataset 编辑数据集
func EditDataset(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addDatasetParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	id := params.Id
	versionDesc := helpers.Trim(params.VersionDesc)
	name := helpers.Trim(params.Name)
	desc := helpers.Trim(params.Desc)
	remark := helpers.Trim(params.Remark)
	objectIdList := params.ObjectIds
	dimensionIdList := params.DimensionIds
	purposeIdList := params.PurposeIds
	metricConf := params.MetricConfig
	sourceType := params.SourceType
	sourceConf := params.SourceConfig

	// 参数check
	if id <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id params error")
	}
	if len(versionDesc) > VersionDescLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "version_desc params error")
	}

	nameLen := len(name)
	if nameLen <= 0 || nameLen > NameLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "name params error")
	}
	if len(desc) > DatasetDescLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "desc params error")
	}
	if len(remark) > DatasetRemarkLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "remark params error")
	}
	if len(objectIdList) > TagIdNumLimit || len(dimensionIdList) > TagIdNumLimit || len(purposeIdList) > TagIdNumLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "tag select limit")
	}
	if !checkSourceType(sourceType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "source_type params error")
	}

	// dataset 查询
	datasetM := models.Dataset{}
	datasetInfo, err := datasetM.GetById(ctx, id)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if datasetInfo.ID <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id params error")
	}

	// name check
	datasetRaw, err := datasetM.GetByName(ctx, name)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if datasetRaw.ID > 0 && datasetRaw.ID != datasetInfo.ID {
		return helpers.FailReturn(helpers.ParamErrorCode, "name repeat")
	}

	// 删除dataset relate
	relateCond := models.TagRelateCond{
		DatasetIdList: []int64{int64(datasetInfo.ID)},
	}
	tagRelateM := models.DatasetTagRelate{}
	err = tagRelateM.Delete(ctx, relateCond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据修改
	objectIdByte, _ := json.Marshal(objectIdList)
	dimensionIdByte, _ := json.Marshal(dimensionIdList)
	purposeIdByte, _ := json.Marshal(purposeIdList)
	sourceConfByte, _ := json.Marshal(sourceConf)
	metricConfByte, _ := json.Marshal(metricConf)
	datasetInfo.Name = name
	datasetInfo.VersionDesc = versionDesc
	datasetInfo.SourceType = sourceType
	datasetInfo.Desc = desc
	datasetInfo.Remark = remark
	datasetInfo.ObjectIds = string(objectIdByte)
	datasetInfo.DimensionIds = string(dimensionIdByte)
	datasetInfo.PurposeIds = string(purposeIdByte)
	datasetInfo.SourceConfig = string(sourceConfByte)
	datasetInfo.MetricConfig = string(metricConfByte)
	err = datasetInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 写入dataset relate
	tagIdList := append(append(objectIdList, dimensionIdList...), purposeIdList...)
	err = batchAddRelate(ctx, int64(datasetInfo.ID), tagIdList)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, err.Error())
	}

	// dataset log
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	addDatasetLog(ctx, adminInfo.AdminId, datasetInfo.ID, models.OperateTypeModify)

	return helpers.SuccReturn(nil)
}

// dataset log list
type datasetLogParams struct {
	Id          int64          `json:"id"`
	Limit       def.IntType    `json:"limit"`
	Page        def.IntType    `json:"page"`
	OperateType []def.EnumType `json:"operate_type"`
}

func GetDatasetLog(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params datasetLogParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	OperateTypeList := params.OperateType
	datasetId := params.Id
	limit := params.Limit
	page := params.Page

	// 参数check
	limit, page = formatPaging(limit, page)

	cond := models.DatasetLogCond{
		OperateTypeList: OperateTypeList,
		DatasetId:       datasetId,
	}
	datasetLogM := models.DatasetLog{}
	datasetLogList, err := datasetLogM.GetList(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 获取总数
	total, err := datasetLogM.Count(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var datasetIdList []int64
	var adminIdList []int64
	for _, item := range datasetLogList {
		datasetIdList = append(datasetIdList, int64(item.DatasetId))
		adminIdList = append(adminIdList, int64(item.AdminId))
	}

	// dataset 信息
	datasetM := models.Dataset{}
	datasetList, err := datasetM.GetByIds(ctx, datasetIdList)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	datasetMap := make(map[int64]models.Dataset)
	for _, item := range datasetList {
		datasetMap[int64(item.ID)] = item
	}

	// 管理员信息
	adminM := models.Admin{}
	adminList, err := adminM.GetByIds(ctx, adminIdList)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	adminMap := make(map[int64]models.Admin)
	for _, item := range adminList {
		adminMap[int64(item.ID)] = item
	}

	var items []any
	for _, item := range datasetLogList {
		val := item.SwapData()

		val["dataset"] = nil
		if datasetData, ok := datasetMap[int64(item.DatasetId)]; ok {
			val["dataset"] = datasetData.SwapData()
		}

		val["admin_name"] = ""
		if adminData, ok := adminMap[int64(item.AdminId)]; ok {
			val["admin_name"] = adminData.Username
		}
		items = append(items, val)
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 数据集列表
type datasetListParams struct {
	Keyword        string         `json:"keyword"`
	Limit          def.IntType    `json:"limit"`
	Page           def.IntType    `json:"page"`
	ObjectIds      []int64        `json:"object_ids"`
	DimensionIds   []int64        `json:"dimension_ids"`
	PurposeIds     []int64        `json:"purpose_ids"`
	SourceTypeList []def.EnumType `json:"source_type_list"`
	Order          int64          `json:"order"`
	OrderBy        string         `json:"order_by"`
}

func GetDatasetList(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params datasetListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit := params.Limit
	page := params.Page
	keyword := helpers.Trim(params.Keyword)
	objectIds := params.ObjectIds
	dimensionIds := params.DimensionIds
	purposeIds := params.PurposeIds
	sourceTypeList := params.SourceTypeList
	order := params.Order
	orderBy := helpers.Trim(params.OrderBy)

	// 参数check
	limit, page = formatPaging(limit, page)

	if len(keyword) > KeywordLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "keyword params error")
	}
	if len(objectIds) > IdNumLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "object_ids params error")
	}
	if len(dimensionIds) > IdNumLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "dimension_ids params error")
	}
	if len(purposeIds) > IdNumLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "purpose_ids params error")
	}
	if len(sourceTypeList) > SourceNumLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "source_type_list params error")
	}
	orderStr, err := getListOrder(orderBy, order)
	if err != nil {
		return helpers.FailReturn(helpers.ParamErrorCode, "order_by params error")
	}

	var datasetIdList []int64
	if len(objectIds) > 0 || len(dimensionIds) > 0 || len(purposeIds) > 0 {
		tagIdList := append(append(objectIds, dimensionIds...), purposeIds...)
		relateCond := models.TagRelateCond{
			MixIdList: tagIdList,
		}

		tagRelateM := models.DatasetTagRelate{}
		tagRelateList, err := tagRelateM.GetList(ctx, relateCond, 2000)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}

		for _, val := range tagRelateList {
			datasetIdList = append(datasetIdList, int64(val.DatasetId))
		}
	}

	datasetCond := models.DatasetCond{
		IdList:         datasetIdList,
		Keyword:        keyword,
		SourceTypeList: sourceTypeList,
		IsNew:          true,
	}
	datasetM := models.Dataset{}
	datasetList, err := datasetM.GetList(ctx, datasetCond, limit, page, orderStr)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	total, err := datasetM.Count(ctx, datasetCond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// tag map
	tagMap, err := getTagMap(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, err.Error())
	}

	// 返回数据
	var items []any
	for _, item := range datasetList {
		tmp := item.SwapData()
		tmp["object_data"] = getTagData(tagMap, item.ObjectIds)
		tmp["dimension_data"] = getTagData(tagMap, item.DimensionIds)
		tmp["purpose_data"] = getTagData(tagMap, item.PurposeIds)

		items = append(items, tmp)
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 获取 bos 地址
type datasetUrlParams struct {
	Ids []int64 `json:"ids"`
}

func GetDatasetUrl(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params datasetUrlParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	ids := params.Ids
	if len(ids) <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "ids params error")
	}

	datasetM := models.Dataset{}
	datasetList, err := datasetM.GetByIds(ctx, ids)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var result []string
	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	for _, item := range datasetList {
		bucket, object := helpers.DealBosFileUrl(item.FileUrl)

		bosUrl, err := bce.GenerateObjectUrl(bucket, object, bce.ObjectUrlExpireTime)
		if err != nil {
			go helpers.HelixNotice(ctx, "--- bos GenerateObject err ----"+err.Error())
		}
		result = append(result, bosUrl)

		// 写入 dataset log
		addDatasetLog(ctx, adminInfo.AdminId, item.ID, models.OperateTypeDownload)
	}

	return helpers.SuccReturn(result)
}

// 导出数据集信息
func GetDatasetExport(ctx context.Context, req ghttp.Request) ghttp.Response {
	cond := models.DatasetCond{}
	datasetM := models.Dataset{}
	datasetList, err := datasetM.GetList(ctx, cond, 10000, 1)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 获取tagMap
	tagMap, err := getTagMap(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 获取adminMap
	adminMap := getAllAdminMap(ctx)

	var items []any
	for _, item := range datasetList {
		objectSlice := getTagSlice(tagMap, item.ObjectIds)
		dimensionSlice := getTagSlice(tagMap, item.ObjectIds)
		purposeSlice := getTagSlice(tagMap, item.ObjectIds)

		// metric
		var metricConf []metricConfig
		_ = json.Unmarshal([]byte(item.MetricConfig), &metricConf)

		var metricSlice []string
		for _, val := range metricConf {
			metricStr := val.Metric + ":" + strconv.Itoa(int(val.Num))
			metricSlice = append(metricSlice, metricStr)
		}

		// source
		var sourceConf sourceConfig
		_ = json.Unmarshal([]byte(item.SourceConfig), &sourceConf)

		// admin
		var adminName string
		if val, ok := adminMap[int(item.AdminId)]; ok {
			adminName = val
		}

		tmp := map[string]any{
			"name":           item.Name,
			"desc":           item.Desc,
			"remark":         item.Remark,
			"object":         strings.Join(objectSlice, ","),
			"dimension":      strings.Join(dimensionSlice, ","),
			"purpose":        strings.Join(purposeSlice, ","),
			"size":           item.Size,
			"metric_config":  strings.Join(metricSlice, ","),        // 指标数据
			"source":         models.SourceTypeMap[item.SourceType], // 数据源类型
			"source_name":    sourceConf.Name,                       // 数据源名称
			"source_mark":    sourceConf.Desc,                       // 数据源备注
			"source_version": sourceConf.Mark,                       // 数据源版本
			"admin_name":     adminName,                             // 负责人
		}
		items = append(items, tmp)
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// get check
func getListOrder(orderBy string, order int64) (string, error) {
	if orderBy == "" {
		return "", nil
	}

	if orderBy != "like" && orderBy != "size" && orderBy != "date" {
		return "", errors.New("order_by params error")
	}
	switch orderBy {
	case "like":
		orderBy = "is_like"
		break
	case "size":
		break
	case "date":
		orderBy = "created_at"
		break
	default:
		return "", errors.New("order_by params error")
	}

	orderStr := orderBy + " asc"
	if order == -1 {
		orderStr = orderBy + " desc"
	}
	return orderStr, nil
}

// tag data
func getTagData(tagMap map[int64]models.Tag, tagIdStr string) []map[string]any {
	var tagIdList []int64
	_ = json.Unmarshal([]byte(tagIdStr), &tagIdList)

	var tagData []map[string]any
	for _, val := range tagIdList {
		if _, ok := tagMap[val]; !ok {
			continue
		}

		tmpMap := map[string]any{
			"id":   val,
			"name": tagMap[val].Name,
		}
		tagData = append(tagData, tmpMap)
	}

	return tagData
}

// tag name slice
func getTagSlice(tagMap map[int64]models.Tag, tagIdStr string) []string {
	var tagIdList []int64
	_ = json.Unmarshal([]byte(tagIdStr), &tagIdList)

	var tagSlice []string
	for _, val := range tagIdList {
		if _, ok := tagMap[val]; !ok {
			continue
		}

		tagSlice = append(tagSlice, tagMap[val].Name)
	}

	return tagSlice
}

// tag map
func getTagMap(ctx context.Context) (map[int64]models.Tag, error) {
	tagMap := make(map[int64]models.Tag)

	tagM := models.Tag{}
	var tagCond models.TagCond
	tagList, err := tagM.GetList(ctx, tagCond, 2000, 1)
	if err != nil {
		return tagMap, errors.New("data error, please repeat")
	}

	for _, val := range tagList {
		tagMap[int64(val.ID)] = val
	}
	return tagMap, nil
}

// batch add relate tag
func batchAddRelate(ctx context.Context, datasetId int64, tagIdList []int64) error {
	tagM := models.Tag{}
	tagList, err := tagM.GetByIds(ctx, tagIdList)
	if err != nil {
		return errors.New("data error, please repeat")
	}

	tagRelate := models.DatasetTagRelate{}
	for _, tag := range tagList {
		tagRelateNew := models.DatasetTagRelate{
			TagId:     tag.ID,
			DatasetId: datasetId,
			Type:      tag.Type,
			ParentId:  tag.ParentId,
		}
		if tag.ParentId == 0 {
			tagRelateNew.ParentId = tag.ID
		}

		tagRelate.Add(ctx, tagRelateNew)
	}

	return nil
}

// add datasetLog
func addDatasetLog(ctx context.Context, adminId int64, datasetId int64, operateType def.EnumType) error {
	datasetLogNew := models.DatasetLog{
		AdminId:     adminId,
		DatasetId:   datasetId,
		OperateType: operateType,
	}

	_, err := datasetLogNew.Add(ctx, datasetLogNew)
	return err
}
