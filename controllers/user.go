package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"

	bcehttp "github.com/baidubce/bce-sdk-go/http"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

// 添加白名单
type whiteDelParams struct {
	Username string `json:"username"`
}

func DelWhite(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params whiteDelParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	username := helpers.Trim(params.Username)

	redis.SRem(ctx, redis.UserWhiteKey, username)

	return helpers.SuccReturn(nil)
}

// 添加白名单
type whiteAddParams struct {
	UsernameList []string `json:"username_list"`
}

func AddWhite(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params whiteAddParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	var userIdList []int64
	for _, s := range params.UsernameList {
		userId, err := strconv.Atoi(s)
		if err != nil {
			continue
		}
		userIdList = append(userIdList, int64(userId))
	}
	// 查询用户
	userM := models.User{}
	userList, err := userM.GetByIds(ctx, userIdList)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	for _, user := range userList {
		// 写入redis
		redis.SAdd(ctx, redis.UserWhiteKey, user.ID)
	}

	return helpers.SuccReturn(nil)
}

// 设置白名单开关
type whiteSetParams struct {
	Operate string `json:"operate"`
}

func SetWhite(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params whiteSetParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	operate := params.Operate
	if operate == "open" {
		redis.Set(ctx, redis.UserWhiteSwitchKey, 1, 86400*3660)
	} else {
		redis.Set(ctx, redis.UserWhiteSwitchKey, 0, 86400*3660)
	}

	return helpers.SuccReturn(nil)
}
func ListWhite(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取白名单信息
	resList := redis.SMembers(ctx, redis.UserWhiteKey)

	// 获取开关
	open := false
	openRes := redis.Get(ctx, redis.UserWhiteSwitchKey)
	if openRes == "1" {
		open = true
	}

	result := map[string]any{
		"open":          open,
		"username_list": resList,
	}
	return helpers.SuccReturn(result)
}

// 获取订阅用户
type subscribeListParams struct {
	Limit int `json:"limit"`
	Page  int `json:"page"`
}

func ListSubscribe(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params subscribeListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit := params.Limit
	page := params.Page

	// 参数check
	limit, page = formatPaging(limit, page)

	listM := models.Subscribe{}
	total, err := listM.CountByCond(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	dataList, err := listM.GetList(ctx, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据处理
	var items []any
	for _, subscribe := range dataList {
		items = append(items, subscribe.SwapData())
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 获取用户信息
type userListParams struct {
	Limit                int            `json:"limit"`
	Page                 int            `json:"page"`
	Type                 def.EnumType   `json:"type"`
	MinLoginTime         int64          `json:"min_login_time"`
	MaxLoginTime         int64          `json:"max_login_time"`
	MinActiveTime        int64          `json:"min_active_time"`
	MaxActiveTime        int64          `json:"max_active_time"`
	MinChpcChangeTime    int64          `json:"min_chpc_change_time"`
	MaxChpcChangeTime    int64          `json:"max_chpc_change_time"`
	MinConsoleChangeTime int64          `json:"min_console_change_time"`
	MaxConsoleChangeTime int64          `json:"max_console_change_time"`
	Keyword              string         `json:"keyword"`
	Remark               string         `json:"remark"`
	TrialTaskType        []def.EnumType `json:"trial_task_type"`
	TrialStatus          []def.EnumType `json:"trial_status"`
	Priority             []string       `json:"priority"`
	Discount             []int          `json:"discount"`
}

func ListUser(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params userListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit := params.Limit
	page := params.Page
	userType := params.Type
	minLoginTime := params.MinLoginTime
	maxLoginTime := params.MaxLoginTime
	minActiveTime := params.MinActiveTime
	maxActiveTime := params.MaxActiveTime
	keywords := strings.Split(helpers.Trim(params.Keyword), ",")
	if maxLoginTime > 0 {
		maxLoginTime = maxLoginTime + 86400
	}
	if maxActiveTime > 0 {
		maxActiveTime = maxActiveTime + 86400
	}

	// 参数check
	limit, page = formatPaging(limit, page)
	if userType != 0 && !checkUserType(userType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "type params error")
	}

	var userIdList []int64
	if minActiveTime > 0 && maxActiveTime > 0 {
		requestM := models.RequestLog{}
		requestCond := models.RequestCond{
			GtCreatedAt: helpers.UnixToTimeStr(minActiveTime),
			LtCreatedAt: helpers.UnixToTimeStr(maxActiveTime),
		}
		requestList, err := requestM.GetList(ctx, requestCond)
		if err != nil {
			// 处理错误，例如返回或记录日志
			helpers.ReturnLogError(ctx, err)
			// 在这里，你可能需要决定如何处理错误，是继续执行还是返回一个错误给调用者
			// return // 或者其他适当的错误处理逻辑
			return helpers.FailReturn(helpers.DBErrorCode, "helix_request_log,error")
		}
		for _, requestLog := range requestList {
			userIdList = append(userIdList, requestLog.UserId)
		}
	}

	// 获取用户信息
	cond := models.UserCond{
		Type:                 userType,
		Keyword:              keywords,
		UserIdList:           userIdList,
		GtCreatedAt:          helpers.UnixToTimeStr(minLoginTime),
		LtCreatedAt:          helpers.UnixToTimeStr(maxLoginTime),
		FilterUserIdList:     getFilterUserId(ctx),
		MinChpcChangeTime:    helpers.UnixToTimeStr(params.MinChpcChangeTime),
		MaxChpcChangeTime:    helpers.UnixToTimeStr(params.MaxChpcChangeTime),
		MinConsoleChangeTime: helpers.UnixToTimeStr(params.MinConsoleChangeTime),
		MaxConsoleChangeTime: helpers.UnixToTimeStr(params.MaxConsoleChangeTime),
		Remark:               params.Remark,
		Priority:             params.Priority,
		Discount:             params.Discount,
	}

	userM := models.User{}
	totalUser, err := userM.Count(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	userList, err := userM.GetList(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	// 数据组装
	totalTrial := 0
	var items []any
	adminMap := getAllAdminMap(ctx)
	for _, user := range userList {
		userInfo := user.SwapData()
		userInfo["child_list"] = nil
		userInfo["coupon_list"] = nil
		userInfo["cost_coupon"] = float64(0)
		userInfo["cash"] = float64(0)
		if user.Type == (uint64(models.UserTypeCharge)) {
			// 请求余额信息
			fund, _ := services.GetAccountFund(ctx, user.RealID, user.Source)
			userInfo["cash"] = fund.Cash
		}
		if user.Type == (uint64(models.UserTypeCharge)) || user.Type == (uint64(models.UserTypeTry)) {
			trialList, _ := getTrialList(ctx, int64(user.ID), params.TrialTaskType, params.TrialStatus, adminMap)
			userInfo["trial_list"] = trialList
			// 代金券信息
			couponList, costCoupon, _ := getCouponList(ctx, int64(user.ID), params.TrialTaskType, params.TrialStatus, adminMap)
			userInfo["coupon_list"] = couponList
			userInfo["cost_coupon"] = costCoupon
			totalTrial += len(trialList) + len(couponList)
		}
		// 点券信息
		voucherList, err := getVoucherList(ctx, int64(user.ID), params.TrialTaskType, params.TrialStatus, adminMap)
		if err != nil {
			return helpers.FailReturn(helpers.DBErrorCode, err.Error())
		}
		userInfo["voucher_list"] = voucherList
		totalTrial += len(voucherList)
		items = append(items, userInfo)
	}

	result := map[string]any{
		"total_user":  totalUser,
		"total_trial": totalTrial,
		"items":       items,
	}
	return helpers.SuccReturn(result)
}

// 编辑用户
type userDelParams struct {
	UserId int64 `json:"user_id"`
}

func DelUser(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params userDelParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	userId := params.UserId

	// 获取用户
	userM := models.User{}
	userInfo, err := userM.GetUserById(ctx, userId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	if userInfo.ID > 0 {
		userInfo.Type = uint64(models.UserTypeNormal)
		_ = userInfo.Save(ctx)
	}
	return helpers.SuccReturn(nil)
}

// 编辑用户
type userEditParams struct {
	UserId int64  `json:"user_id"`
	Remark string `json:"remark"`
}

func EditUser(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params userEditParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	userId := params.UserId
	remark := helpers.Trim(params.Remark)

	remarkLen := len(remark)
	if remarkLen <= 0 || remarkLen > 512 {
		return helpers.FailReturn(helpers.ParamErrorCode, "remark params error")
	}

	userM := models.User{}
	userInfo, err := userM.GetUserById(ctx, userId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	if userInfo.ID > 0 {
		userInfo.Remark = remark
		_ = userInfo.Save(ctx)
	}
	return helpers.SuccReturn(nil)
}

// 添加用户
type userChangeParams struct {
	UserType        def.EnumType `json:"user_type"`
	UserID          int64        `json:"user_id"`
	Remark          string       `json:"remark"`
	Discount        int          `json:"discount"`
	DiscountEndTime int64        `json:"discount_end_time"`
}

func ChangeUser(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params userChangeParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	userType := params.UserType
	remark := helpers.Trim(params.Remark)

	// 参数检验
	if userType != 0 && !checkUserType(userType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "user_type params error")
	}
	if len(remark) > 512 {
		return helpers.FailReturn(helpers.ParamErrorCode, "remark params error")
	}

	// 用户check
	userM := models.User{}
	userInfo, err := userM.GetUserById(ctx, params.UserID)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	if userInfo.ID <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "username is invalid")
	}

	// 数据处理
	userInfo.Type = uint64(userType)
	userInfo.Remark = remark
	if params.Discount != -1 {
		userInfo.Discount = params.Discount
	}
	if params.Discount == 1 && params.DiscountEndTime == 0 {
		userInfo.DiscountEndTime = time.Now().AddDate(1, 0, 0)
	}
	if params.DiscountEndTime != 0 {
		userInfo.DiscountEndTime = time.Unix(params.DiscountEndTime, 0)
	}
	_ = userInfo.Save(ctx)

	return helpers.SuccReturn(nil)
}

// 获取用户信息
type userDownloadParams struct {
	Type                 def.EnumType `json:"type"`
	MinLoginTime         int64        `json:"min_login_time"`
	MaxLoginTime         int64        `json:"max_login_time"`
	MinActiveTime        int64        `json:"min_active_time"`
	MaxActiveTime        int64        `json:"max_active_time"`
	MinChpcChangeTime    int64        `json:"min_chpc_change_time"`
	MaxChpcChangeTime    int64        `json:"max_chpc_change_time"`
	MinConsoleChangeTime int64        `json:"min_console_change_time"`
	MaxConsoleChangeTime int64        `json:"max_console_change_time"`
	Keyword              string       `json:"keyword"`
	Remark               string       `json:"remark"`
	Priority             []string     `json:"priority"`
	Discount             []int        `json:"discount"`
}

func DownloadUser(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params userDownloadParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	userType := params.Type
	minLoginTime := params.MinLoginTime
	maxLoginTime := params.MaxLoginTime
	minActiveTime := params.MinActiveTime
	maxActiveTime := params.MaxActiveTime
	keywords := strings.Split(helpers.Trim(params.Keyword), ",")
	if maxLoginTime > 0 {
		maxLoginTime = maxLoginTime + 86400
	}
	if maxActiveTime > 0 {
		maxActiveTime = maxActiveTime + 86400
	}

	// 参数check
	if userType != 0 && !checkUserType(userType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "type params error")
	}

	var userIdList []int64
	if minActiveTime > 0 && maxActiveTime > 0 {
		requestM := models.RequestLog{}
		requestCond := models.RequestCond{
			GtCreatedAt: helpers.UnixToTimeStr(minActiveTime),
			LtCreatedAt: helpers.UnixToTimeStr(maxActiveTime),
		}
		requestList, err := requestM.GetList(ctx, requestCond)
		if err != nil {
			// 处理错误，例如返回或记录日志
			helpers.ReturnLogError(ctx, err)
			// 在这里，你可能需要决定如何处理错误，是继续执行还是返回一个错误给调用者
			// return // 或者其他适当的错误处理逻辑
			return helpers.FailReturn(helpers.DBErrorCode, "helix_request_log,error")
		}
		for _, requestLog := range requestList {
			userIdList = append(userIdList, requestLog.UserId)
		}
	}

	// 获取用户信息
	cond := models.UserCond{
		Type:                 userType,
		Keyword:              keywords,
		UserIdList:           userIdList,
		GtCreatedAt:          helpers.UnixToTimeStr(minLoginTime),
		LtCreatedAt:          helpers.UnixToTimeStr(maxLoginTime),
		FilterUserIdList:     getFilterUserId(ctx),
		MinChpcChangeTime:    helpers.UnixToTimeStr(params.MinChpcChangeTime),
		MaxChpcChangeTime:    helpers.UnixToTimeStr(params.MaxChpcChangeTime),
		MinConsoleChangeTime: helpers.UnixToTimeStr(params.MinConsoleChangeTime),
		MaxConsoleChangeTime: helpers.UnixToTimeStr(params.MaxConsoleChangeTime),
		Remark:               params.Remark,
		Priority:             params.Priority,
		Discount:             params.Discount,
	}

	userM := models.User{}
	totalUser, err := userM.Count(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	userList, err := userM.GetAllList(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	// 数据组装
	var items []any
	for _, user := range userList {
		userInfo := user.SwapData()
		items = append(items, userInfo)
	}

	result := map[string]any{
		"total_user": totalUser,
		"items":      items,
	}
	return helpers.SuccReturn(result)
}

// 获取试用信息
func getTrialList(ctx context.Context, userId int64, taskType []def.EnumType,
	status []def.EnumType, adminMap map[int]string) ([]any, error) {
	var trials []any

	trialM := models.Trial{}
	trialList, err := trialM.GetTrail(ctx, userId, taskType, status, 100, 1)
	if err != nil {
		return trials, errors.New("data error, please repeat")
	}

	// 试用记录
	for _, trial := range trialList {
		tTmp := trial.SwapData()
		if trial.Type == (models.TrialTypeTime) {
			tTmp["limit_day"] = (trial.EndTime.Unix() - trial.StartTime.Unix()) / 86400
			tTmp["used_day"] = (time.Now().Unix() - trial.StartTime.Unix()) / 86400
		}

		// 操作人员
		tTmp["admin_name"] = ""
		if name, ok := adminMap[int(trial.AdminId)]; ok {
			tTmp["admin_name"] = name
		}
		trials = append(trials, tTmp)
	}
	return trials, nil
}

// get coupon
func getCouponList(ctx context.Context, userId int64, taskType []def.EnumType,
	status []def.EnumType, adminMap map[int]string) ([]any, float64, error) {
	var itemList []any

	couponM := models.Coupon{}
	couponList, err := couponM.GetList(ctx, userId, taskType, status, 100, 1)
	if err != nil {
		return itemList, 0, errors.New("data error, please repeat")
	}

	costCoupon := float64(0)
	for _, coupon := range couponList {
		tTmp := coupon.SwapData()

		tTmp["admin_name"] = ""
		if name, ok := adminMap[int(coupon.AdminId)]; ok {
			tTmp["admin_name"] = name
		}
		itemList = append(itemList, tTmp)
		costCoupon += coupon.Amount - coupon.RestAmount
	}

	return itemList, costCoupon, nil
}

func getVoucherList(ctx context.Context, userId int64, taskType []def.EnumType,
	status []def.EnumType, adminMap map[int]string) ([]any, error) {
	var itemList []any

	voucherM := models.Voucher{}
	param := models.VoucherGetListParam{
		UserID:   userId,
		TaskType: taskType,
		Status:   status,
		Limit:    100,
		Page:     1,
		OrderBy:  "id",
		Order:    models.VoucherListOrderDesc,
	}
	voucherList, err := voucherM.GetList(ctx, &param)
	if err != nil {
		return itemList, errors.New("data error, please repeat")
	}

	for _, voucher := range voucherList {
		tTmp := voucher.SwapData()

		tTmp["admin_name"] = ""
		if name, ok := adminMap[int(voucher.AdminID)]; ok {
			tTmp["admin_name"] = name
		}
		itemList = append(itemList, tTmp)
	}

	return itemList, nil
}

// 修改用户优先级
type userChangePriorityParams struct {
	UserID   int64  `json:"user_id"`
	Priority string `json:"priority"`
}

func ChangeUserPriority(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params userChangePriorityParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	// 查询用户信息
	userM := models.User{}
	userInfo, err := userM.GetUserById(ctx, params.UserID)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	if userInfo.ID <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "userID is invalid")
	}
	if params.Priority == userInfo.Priority {
		return helpers.SuccReturn(nil)
	}

	// 请求CHPC更新用户qos
	ak := chpc.NewCHPClient().AK
	sk := chpc.NewCHPClient().SK
	uri := "/v1/helix/user/qos"
	request := &bcehttp.Request{}
	request.SetParam("qos", models.UserPriorityToQos[params.Priority])
	request.SetParam("userID", userInfo.UserID)
	clientConf := bcehttp.ClientConfig{}
	bcehttp.InitClient(clientConf)
	request.SetProtocol("http")
	request.SetMethod("PUT")
	request.SetHost(chpc.NewCHPClient().Endpoint)
	request.SetUri(uri)
	request.SetHeader("Host", chpc.NewCHPClient().Endpoint)
	request.SetHeader("Content-Type", "application/json")
	chpc.GenerateStsAuthHeader(request, ak, sk)

	resp, err := bcehttp.Execute(request)
	if resp.StatusCode() != 200 || err != nil {
		helpers.LogError(ctx, err)
		fmt.Println(resp, err)
		go helpers.HelixNotice(ctx, "--- change user priority error ---"+err.Error())
		return helpers.FailReturn(helpers.ServiceErrorCode, err.Error())
	}

	// 更新用户信息
	userInfo.Priority = params.Priority
	if err := userInfo.Save(ctx); err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}
