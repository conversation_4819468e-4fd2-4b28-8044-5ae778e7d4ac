package controllers

import (
	"context"
	"encoding/json"
	"icode.baidu.com/helix_web/library/def"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// 获取tag list
type tagListParams struct {
	Type  def.EnumType `json:"type"`
	Limit int          `json:"limit"`
	Page  int          `json:"page"`
}

func GetTagList(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params tagListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	if params.Type > 0 && !checkTagType(params.Type) {
		return helpers.FailReturn(helpers.ParamErrorCode, "type params error")
	}

	// param check
	limit, page := formatPaging(params.Limit, params.Page)

	// 获取一级标签数据
	tagM := models.Tag{}
	cond := models.TagCond{
		Type:         params.Type,
		ParentIdList: []int64{int64(models.DefaultParentId)},
	}
	tagList, err := tagM.GetList(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	total, err := tagM.CountByCond(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var parentIdList []int64
	for _, item := range tagList {
		parentIdList = append(parentIdList, int64(item.ID))
	}

	// 获取一级标签的数据集统计
	relateM := models.DatasetTagRelate{}
	relateCond := models.TagRelateCond{
		ParentIdList: parentIdList,
	}
	firstTagList, err := relateM.GroupSum(ctx, relateCond, "parent_id")
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	firstTagMap := make(map[int64]int64)
	for _, val := range firstTagList {
		firstTagMap[val.Field] = val.Total
	}

	// 获取对应二级标签
	cond = models.TagCond{
		ParentIdList: parentIdList,
	}
	tagChildList, err := tagM.GetList(ctx, cond, 200, 1)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	var childTagIdList []int64
	for _, item := range tagChildList {
		childTagIdList = append(childTagIdList, (item.ID))
	}

	// 获取二级标签的数据集统计
	relateCond = models.TagRelateCond{
		TagIdList: childTagIdList,
	}
	secondTagList, err := relateM.GroupSum(ctx, relateCond, "tag_id")
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	secondTagMap := make(map[int64]int64)
	for _, val := range secondTagList {
		secondTagMap[val.Field] = val.Total
	}

	// 关联二级标签和其数据集 统计数据
	tagChildMap := make(map[int64][]any)
	for _, item := range tagChildList {
		tmp := item.SwapData()
		tmp["dataset_num"] = 0
		if val, ok := secondTagMap[(item.ID)]; ok {
			tmp["dataset_num"] = val
		}

		if val, ok := tagChildMap[(item.ParentId)]; ok {
			tagChildMap[(item.ParentId)] = append(val, tmp)
		} else {
			tagChildMap[(item.ParentId)] = []any{tmp}
		}
	}

	// 拼接数据
	var items []any
	for _, item := range tagList {
		tmp := item.SwapData()

		tmp["dataset_num"] = 0
		if val, ok := firstTagMap[(item.ID)]; ok {
			tmp["dataset_num"] = val
		}

		tmp["child"] = nil
		if val, ok := tagChildMap[(item.ID)]; ok {
			tmp["child"] = val
		}
		items = append(items, tmp)
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 添加addTagParams
type addTagParams struct {
	Type     def.EnumType `json:"type"`
	Name     string       `json:"name"`
	ParentId int64        `json:"parent_id"`
}

func AddTag(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addTagParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	name := helpers.Trim(params.Name)
	parentId := params.ParentId
	if !checkTagType(params.Type) {
		return helpers.FailReturn(helpers.ParamErrorCode, "type params error")
	}

	nameLen := len(name)
	if nameLen <= 0 || nameLen > NameLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "name params error")
	}

	// name check
	tagM := models.Tag{}
	tagData, err := tagM.GetByName(ctx, name)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	if tagData.ID > 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "name repeat")
	}

	if parentId > 0 {
		tagInfo, err := tagM.GetById(ctx, parentId)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}
		if tagInfo.ID <= 0 || tagInfo.ParentId != models.DefaultParentId {
			return helpers.FailReturn(helpers.ParamErrorCode, "parent_id params error")
		}
	} else {
		parentId = 0
	}

	adminInfo := ctx.Value("admin_info").(models.AdminInfo)
	tagNew := models.Tag{
		Type:     (params.Type),
		ParentId: (parentId),
		AdminId:  (adminInfo.AdminId),
		Name:     name,
	}
	tagNew.Add(ctx, tagNew)

	return helpers.SuccReturn(nil)
}

// 添加editTagParams
type editTagParams struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

func EditTag(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params editTagParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	name := helpers.Trim(params.Name)

	nameLen := len(name)
	if nameLen <= 0 || nameLen > NameLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "name params error")
	}
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id params error")
	}

	tagM := models.Tag{}
	tagInfo, err := tagM.GetById(ctx, params.Id)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	if tagInfo.ID > 0 {
		// check name
		tagData, err := tagM.GetByName(ctx, name)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}
		if tagData.ID > 0 && tagData.ID != tagInfo.ID {
			return helpers.FailReturn(helpers.ParamErrorCode, "name repeat")
		}

		tagInfo.Name = name
		err = tagInfo.Save(ctx)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}
	}
	return helpers.SuccReturn(nil)
}

// 添加editTagParams
type delTagParams struct {
	Id int64 `json:"id"`
}

func DelTag(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params delTagParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)

	tagM := models.Tag{}
	tagInfo, err := tagM.GetById(ctx, params.Id)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	if tagInfo.ID > 0 {
		tagInfo.Status = models.StatusDel
		err = tagInfo.Save(ctx)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}

		// 删除dataset 标签
		relateCond := models.TagRelateCond{
			TagIdList: []int64{int64(tagInfo.ID)},
		}
		tagRelateM := models.DatasetTagRelate{}
		tagRelateM.Delete(ctx, relateCond)
	}
	return helpers.SuccReturn(nil)
}
