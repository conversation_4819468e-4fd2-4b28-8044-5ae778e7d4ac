package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

type serialListParams struct {
	Limit    int          `json:"limit"`
	Page     int          `json:"page"`
	Type     def.EnumType `json:"type"`
	FuncType def.EnumType `json:"func_type"`
	DataType def.EnumType `json:"data_type"`
}

func GetSerialList(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params serialListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit := params.Limit
	page := params.Page
	tType := params.Type
	funcType := params.FuncType
	dataType := params.DataType

	// 参数check
	limit, page = formatPaging(limit, page)
	if tType > 0 && !checkTaskType(tType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "type params error")
	}
	if funcType > 0 && !checkFuncType(funcType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "func_type params error")
	}
	if dataType > 0 && !checkDataType(dataType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "data_type params error")
	}

	// 数据拼接
	cond := models.SerialCond{
		Type:     tType,
		FuncType: funcType,
		DataType: dataType,
	}

	serialM := models.Serial{}
	total, err := serialM.CountByCond(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	serialList, err := serialM.GetList(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据组装
	var adminIdList []int64
	for _, serial := range serialList {
		adminIdList = append(adminIdList, int64(serial.AdminId))
	}
	adminMap := getAdminMap(ctx, adminIdList)

	var items []any
	for _, serial := range serialList {
		tmp := serial.SwapData()

		tmp["admin_name"] = ""
		if _, ok := adminMap[int(serial.AdminId)]; ok {
			tmp["admin_name"] = adminMap[int(serial.AdminId)]
		}

		items = append(items, tmp)
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 添加示例
type addSerialParams struct {
	Type       def.EnumType `json:"type"`
	FuncType   def.EnumType `json:"func_type"`
	DataType   def.EnumType `json:"data_type"`
	FileUrl    string       `json:"file_url"`
	Name       string       `json:"name"`
	Desc       string       `json:"desc"`
	MolUrl     string       `json:"mol_url"`
	ProteinUrl string       `json:"protein_url"`
	SampleUrl  string       `json:"sample_url"`
}

func AddSerial(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addSerialParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	tType := params.Type
	funcType := params.FuncType
	dataType := params.DataType
	fileUrl := params.FileUrl

	// 参数check
	if !checkTaskType(tType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "type params error")
	}
	if !checkFuncType(funcType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "func_type params error")
	}
	if !checkDataType(dataType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "data_type params error")
	}

	// 名称
	nameLen := len([]rune(params.Name))
	if nameLen <= 0 || nameLen > AuthorLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "name params error")
	}
	if len([]rune(params.Desc)) > DescLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "desc params error")
	}

	fileUrlLen := len([]rune(fileUrl))
	if fileUrlLen <= 0 || fileUrlLen > UrlLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "file_url params error")
	}
	fileContent := getFileContent(fileUrl)

	// type check
	if funcType == models.FuncTypeTrain && !checkTrainType(tType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "type and func_type error")
	}
	if tType == models.TaskTypeDoubleDrug && dataType == models.DataTypeString {
		return helpers.FailReturn(helpers.ParamErrorCode, "type and data_type error")
	}

	// check serial name
	err := checkSerialName(ctx, tType, params.Name, 0)
	if err != nil {
		return helpers.FailReturn(helpers.ParamErrorCode, err.Error())
	}

	serialNew := models.Serial{
		AdminId:  getAdminId(ctx),
		Type:     tType,
		FuncType: funcType,
		DataType: dataType,
		FileUrl:  fileUrl,
		Name:     params.Name,
		Desc:     params.Desc,
	}

	if funcType == models.FuncTypeForecast && dataType == models.DataTypeString {
		serialNew.Serial = fileContent

		// 分子活性预测
		if tType == models.TaskTypeMolActivity {
			fileContent = strings.Replace(fileContent, "\n", "", -1)
			contentSlice := strings.Split(fileContent, "%")
			if len(contentSlice) < 2 {
				return helpers.FailReturn(helpers.ParamErrorCode, "file content error")
			}

			serialNew.Protein = contentSlice[0]
			serialNew.Serial = contentSlice[1]
		}

		// 抗体抗原
		if tType == models.TaskTypeAntibody || tType == models.TaskTypeNewCrown {
			fileContent = strings.Replace(fileContent, "\n", "", -1)
			contentSlice := strings.Split(fileContent, "%")
			if len(contentSlice) < 2 {
				return helpers.FailReturn(helpers.ParamErrorCode, "file content error")
			}

			conf := map[string]string{
				"heavy_serial": contentSlice[0],
				"light_serial": contentSlice[1],
			}
			if len(contentSlice) >= 3 {
				conf["antigen_serial"] = contentSlice[2]
			}
			confByte, _ := json.Marshal(conf)
			serialNew.Serial = ""
			serialNew.Config = string(confByte)
		}

		// 蛋白突变
		if tType == models.TaskTypeProteinMutation {
			fileContent = strings.Replace(fileContent, "\n", "", -1)
			contentSlice := strings.Split(fileContent, "%")
			if len(contentSlice) < 4 {
				return helpers.FailReturn(helpers.ParamErrorCode, "file content error")
			}

			var point1, point2 []string
			if len(helpers.Trim(contentSlice[1])) > 0 {
				point1 = strings.Split(contentSlice[1], ",")
			}
			if len(helpers.Trim(contentSlice[3])) > 0 {
				point2 = strings.Split(contentSlice[3], ",")
			}
			conf := map[string]any{
				"protein1": contentSlice[0],
				"point1":   point1,
				"protein2": contentSlice[2],
				"point2":   point2,
			}
			confByte, _ := json.Marshal(conf)
			serialNew.Serial = ""
			serialNew.Config = string(confByte)
		}
	}
	if tType == models.TaskTypeProteinFunc {
		serialNew.Serial = ""
	}
	if funcType == models.FuncTypeForecastRNA ||
		funcType == models.SerialFuncTypeForecast3UTR ||
		funcType == models.SerialFuncTypeForecast5UTR {
		serialNew.Serial = fileContent
	}

	// 添加数据
	_, err = serialNew.Add(ctx, serialNew)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	return helpers.SuccReturn(nil)
}

// 更新
type updateSerialParams struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Desc string `json:"desc"`
}

func UpdateSerial(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params updateSerialParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	name := params.Name
	desc := params.Desc

	// 参数check
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id params error")
	}

	nameLen := len([]rune(name))
	if nameLen <= 0 || nameLen > NameLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "name params error")
	}
	if len([]rune(desc)) > DescLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "desc params error")
	}

	// 查询数据
	serialM := models.Serial{}
	serialInfo, err := serialM.GetById(ctx, params.Id)
	if err != nil || serialInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// check serial name
	err = checkSerialName(ctx, serialInfo.Type, name, serialInfo.ID)
	if err != nil {
		return helpers.FailReturn(helpers.ParamErrorCode, err.Error())
	}

	// 修改数据
	serialInfo.Name = name
	serialInfo.Desc = desc
	err = serialInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}

// 设置示例
type setSerialParams struct {
	Id          int64  `json:"id"`
	OperateType string `json:"operate_type"`
}

func SetSerial(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params setSerialParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	operateType := params.OperateType

	// 参数check
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id params error")
	}
	if !checkOperateType(operateType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "operate_type params error")
	}

	serialM := models.Serial{}
	serialInfo, err := serialM.GetById(ctx, params.Id)
	if err != nil || serialInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	if operateType == OperateTypePublish {
		serialInfo.Status = models.StatusOnline

		// 文件个数判断
		if serialInfo.DataType == models.DataTypeFile {
			cond := models.SerialCond{
				Type:     serialInfo.Type,
				FuncType: serialInfo.FuncType,
				DataType: serialInfo.DataType,
				Status:   models.StatusOnline,
			}

			count, err := serialM.CountByCond(ctx, cond)
			if err != nil {
				return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
			}
			if count >= 1 {
				return helpers.FailReturn(helpers.LogicErrorCode, "file publish limit 1")
			}
		}
	} else {
		serialInfo.Status = models.StatusNew
	}

	err = serialInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 提交给调度
	if serialInfo.FuncType != models.FuncTypePretreat {
		_, _ = services.SubmitTask(ctx, serialInfo)
	}
	return helpers.SuccReturn(nil)
}

// 检查serial name
func checkSerialName(ctx context.Context, typ def.EnumType, name string, id int64) error {
	cond := models.SerialCond{
		Type: typ,
		Name: name,
	}

	serialM := models.Serial{}
	serialList, err := serialM.GetList(ctx, cond, 1, 1)
	if err != nil {
		return errors.New("data error, please repeat")
	}
	if len(serialList) >= 1 && serialList[0].ID != id {
		return errors.New("name repeat")
	}

	return nil
}
