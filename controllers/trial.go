package controllers

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"icode.baidu.com/helix_web/library/def"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

type trialAddParams struct {
	UserId    int64        `json:"user_id"`
	TaskType  def.EnumType `json:"task_type"`
	TrailType def.EnumType `json:"trial_type"`
	Num       int64        `json:"num"`
	StartTime int64        `json:"start_time"`
	Remark    string       `json:"remark"`
}

func AddTrial(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params trialAddParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	userId := params.UserId
	taskType := params.TaskType
	trialType := params.TrailType
	num := params.Num
	startTime := params.StartTime
	remark := helpers.Trim(params.Remark)

	// 参数检验
	if !checkTaskType((taskType)) && checkChargeTaskType((taskType)) && taskType != models.TaskTypeHelixVS {
		return helpers.FailReturn(helpers.ParamErrorCode, "task_type params error")
	}
	if len(remark) > 512 {
		return helpers.FailReturn(helpers.ParamErrorCode, "remark params error")
	}
	if num <= 0 || !checkTryType((trialType)) || startTime < time.Now().Unix()-86400 {
		return helpers.FailReturn(helpers.ParamErrorCode, "trail params error")
	}

	// 用户check
	userM := models.User{}
	userInfo, err := userM.GetUserById(ctx, userId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	if userInfo.ID <= 0 || !checkAddTryType((def.EnumType(userInfo.Type))) {
		return helpers.FailReturn(helpers.ParamErrorCode, "user_id is invalid")
	}

	// 创建试用
	formatTime := helpers.UnixToTime(startTime)
	trialNew := models.Trial{
		AdminId:   (getAdminId(ctx)),
		UserId:    def.AutoIDType(userInfo.ID),
		Type:      (trialType),
		UserType:  def.EnumType(userInfo.Type),
		TaskType:  (taskType),
		StartTime: formatTime,
	}
	if trialType == (models.TrialTypeTime) {
		hourNum := time.Duration(24 * num)
		trialNew.EndTime = formatTime.Add(hourNum * time.Hour)
	} else {
		trialNew.LimitNum = (num)
	}
	_, err = trialNew.Add(ctx, trialNew)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}

// 设置试用记录
type trialSetParams struct {
	TrailId int64  `json:"trial_id"`
	Operate string `json:"operate"`
}

func SetTrial(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params trialSetParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	trialId := params.TrailId
	operate := helpers.Trim(params.Operate)
	if operate != "del" && operate != "stop" {
		return helpers.FailReturn(helpers.ParamErrorCode, "remark params error")
	}

	trialM := models.Trial{}
	trialInfo, err := trialM.GetById(ctx, trialId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	if trialInfo.ID <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "trial record empty")
	}

	trialInfo.AdminId = (getAdminId(ctx))
	trialInfo.Status = (models.StatusDel)
	if operate == "stop" {
		trialInfo.Status = (models.TrialStatusLose)
		trialInfo.EndTime = time.Now()
	}
	_ = trialInfo.Save(ctx)

	return helpers.SuccReturn(nil)
}

// 获取用户信息
type trialListParams struct {
	Limit  int   `json:"limit"`
	Page   int   `json:"page"`
	UserId int64 `json:"user_id"`
}

func ListTrial(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params trialListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit := params.Limit
	page := params.Page
	userId := params.UserId

	// 参数check
	limit, page = formatPaging(limit, page)

	// 获取数据
	trialM := models.Trial{}
	total, err := trialM.Count(ctx, userId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	trialList, err := trialM.GetTrail(ctx, userId, nil, nil, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	// 数据处理
	var items []any
	for _, trial := range trialList {
		items = append(items, trial.SwapData())
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

type trialDownloadParams struct {
	Type                 def.EnumType   `json:"type"`
	MinLoginTime         int64          `json:"min_login_time"`
	MaxLoginTime         int64          `json:"max_login_time"`
	MinActiveTime        int64          `json:"min_active_time"`
	MaxActiveTime        int64          `json:"max_active_time"`
	MinChpcChangeTime    int64          `json:"min_chpc_change_time"`
	MaxChpcChangeTime    int64          `json:"max_chpc_change_time"`
	MinConsoleChangeTime int64          `json:"min_console_change_time"`
	MaxConsoleChangeTime int64          `json:"max_console_change_time"`
	Keyword              string         `json:"keyword"`
	Remark               string         `json:"remark"`
	TrialTaskType        []def.EnumType `json:"trial_task_type"`
	TrialStatus          []def.EnumType `json:"trial_status"`
	Priority             []string       `json:"priority"`
	Discount             []int          `json:"discount"`
}

// 下载试用信息
func DownloadTrial(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params trialDownloadParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	userType := params.Type
	minLoginTime := params.MinLoginTime
	maxLoginTime := params.MaxLoginTime
	minActiveTime := params.MinActiveTime
	maxActiveTime := params.MaxActiveTime
	keywords := strings.Split(helpers.Trim(params.Keyword), ",")
	if maxLoginTime > 0 {
		maxLoginTime = maxLoginTime + 86400
	}
	if maxActiveTime > 0 {
		maxActiveTime = maxActiveTime + 86400
	}

	// 参数check
	if userType != 0 && !checkUserType(userType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "type params error")
	}

	var userIdList []int64
	if minActiveTime > 0 && maxActiveTime > 0 {
		requestM := models.RequestLog{}
		requestCond := models.RequestCond{
			GtCreatedAt: helpers.UnixToTimeStr(minActiveTime),
			LtCreatedAt: helpers.UnixToTimeStr(maxActiveTime),
		}
		requestList, err := requestM.GetList(ctx, requestCond)
		if err != nil {
			// 处理错误，例如返回或记录日志
			helpers.ReturnLogError(ctx, err)
			// 在这里，你可能需要决定如何处理错误，是继续执行还是返回一个错误给调用者
			// return // 或者其他适当的错误处理逻辑
			return helpers.FailReturn(helpers.DBErrorCode, "helix_request_log,error")
		}
		for _, requestLog := range requestList {
			userIdList = append(userIdList, requestLog.UserId)
		}
	}

	// 获取用户信息
	cond := models.UserCond{
		Type:                 userType,
		Keyword:              keywords,
		UserIdList:           userIdList,
		GtCreatedAt:          helpers.UnixToTimeStr(minLoginTime),
		LtCreatedAt:          helpers.UnixToTimeStr(maxLoginTime),
		FilterUserIdList:     getFilterUserId(ctx),
		MinChpcChangeTime:    helpers.UnixToTimeStr(params.MinChpcChangeTime),
		MaxChpcChangeTime:    helpers.UnixToTimeStr(params.MaxChpcChangeTime),
		MinConsoleChangeTime: helpers.UnixToTimeStr(params.MinConsoleChangeTime),
		MaxConsoleChangeTime: helpers.UnixToTimeStr(params.MaxConsoleChangeTime),
		Remark:               params.Remark,
		Priority:             params.Priority,
		Discount:             params.Discount,
	}

	userM := models.User{}
	userList, err := userM.GetAllList(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	// 查询试用信息
	adminMap := getAllAdminMap(ctx)
	var items []any
	for _, user := range userList {
		if def.EnumType(user.Type) == (models.UserTypeCharge) || def.EnumType(user.Type) == (models.UserTypeTry) {
			// 试用信息
			trialM := models.Trial{}
			trialList, err := trialM.GetTrail(ctx, def.AutoIDType(user.ID), params.TrialTaskType,
				params.TrialStatus, 1000, 1)
			if err != nil {
				return helpers.FailReturn(helpers.DBErrorCode, err.Error())
			}
			for _, t := range trialList {
				trialData := t.SwapData()
				trialData["admin_name"] = ""
				if name, ok := adminMap[int(t.AdminId)]; ok {
					trialData["admin_name"] = name
				}
				items = append(items, trialData)
			}
			// 代金券信息
			couponM := models.Coupon{}
			couponList, err := couponM.GetList(ctx, def.AutoIDType(user.ID), params.TrialTaskType,
				params.TrialStatus, 1000, 1)
			if err != nil {
				return helpers.FailReturn(helpers.DBErrorCode, err.Error())
			}
			for _, c := range couponList {
				var rangeList []def.EnumType
				_ = json.Unmarshal([]byte(c.RangeList), &rangeList)
				couponData := c.SwapData()
				couponData["range_list"] = rangeList
				couponData["admin_name"] = ""
				if name, ok := adminMap[int(c.AdminId)]; ok {
					couponData["admin_name"] = name
				}
				couponData["trial_type"] = models.CouponType
				items = append(items, couponData)
			}
		}
		// 点券信息
		voucherM := models.Voucher{}
		param := models.VoucherGetListParam{
			UserID:   int64(user.ID),
			TaskType: params.TrialTaskType,
			Status:   params.TrialStatus,
			Limit:    1000,
			Page:     1,
			OrderBy:  "id",
			Order:    models.VoucherListOrderDesc,
		}
		voucherList, err := voucherM.GetList(ctx, &param)
		if err != nil {
			return helpers.FailReturn(helpers.DBErrorCode, err.Error())
		}
		for _, v := range voucherList {
			voucherData := v.SwapData()
			voucherData["admin_name"] = ""
			if name, ok := adminMap[int(v.AdminID)]; ok {
				voucherData["admin_name"] = name
			}
			voucherData["trial_type"] = models.VoucherType
			items = append(items, voucherData)
		}
	}

	result := map[string]any{
		"total_trial": len(items),
		"items":       items,
	}
	return helpers.SuccReturn(result)
}
