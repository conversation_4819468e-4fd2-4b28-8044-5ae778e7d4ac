package controllers

import (
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

type feedbarListParams struct {
	Limit int `json:"limit"`
	Page  int `json:"page"`
}

func GetFeedbarList(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params feedbarListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	limit := params.Limit
	page := params.Page

	// 参数check
	limit, page = formatPaging(limit, page)

	feedbarM := models.Feedbar{}
	total, err := feedbarM.CountByCond(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	feedbarList, err := feedbarM.GetList(ctx, limit, page, "sort asc, display_time desc")
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据组装
	adminIdList := helpers.ColumnList(feedbarList, func(item models.Feedbar) int64 { return int64(item.AdminId) })
	adminMap := getAdminMap(ctx, adminIdList)

	var items []interface{}
	for _, feedbar := range feedbarList {
		tmp := feedbar.SwapData()

		tmp["admin_name"] = ""
		if _, ok := adminMap[int(feedbar.AdminId)]; ok {
			tmp["admin_name"] = adminMap[int(feedbar.AdminId)]
		}
		items = append(items, tmp)
	}

	result := map[string]interface{}{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

type addFeedbarParams struct {
	DisplayTime int64        `json:"display_time"`
	Content     string       `json:"content"`
	ContentEN   string       `json:"content_en"`
	Type        def.EnumType `json:"type"`
	Sort        int64        `json:"sort"`
	Url         string       `json:"url"`
	PicUrl      string       `json:"pic_url"`
}

func AddFeedbar(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addFeedbarParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	content := params.Content
	contentEN := params.ContentEN
	tType := params.Type
	sort := params.Sort
	url := params.Url
	picUrl := params.PicUrl

	// 参数check
	if params.DisplayTime <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "display_time params error")
	}
	if !checkFeedbarType(tType) {
		return helpers.FailReturn(helpers.LogicErrorCode, "type params error")
	}

	contentLen := len([]rune(content))
	if contentLen <= 0 || contentLen > ContentLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "content params error")
	}
	contentENLen := len([]rune(contentEN))
	if contentENLen <= 0 || contentENLen > ContentLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "content_en params error")
	}
	if len([]rune(url)) > UrlLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "url params error")
	}
	if sort <= 0 || sort > 100 {
		sort = 100
	}

	dateT := time.Unix(params.DisplayTime, 0).Format("2006-01-02 15:04:05")
	formatTime, _ := time.ParseInLocation("2006-01-02 15:04:05", dateT, time.Local)

	adminInfo := ctx.Value("admin_info").(models.AdminInfo)

	// 添加数据
	feedbarM := models.Feedbar{}
	feedbarNew := models.Feedbar{
		DisplayTime: formatTime,
		Content:     content,
		ContentEN:   contentEN,
		Url:         url,
		PicUrl:      picUrl,
		Sort:        sort,
		AdminId:     adminInfo.AdminId,
		Type:        tType,
	}
	_, err := feedbarM.Add(ctx, feedbarNew)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}
	return helpers.SuccReturn(nil)
}

// 更新动态栏
type updateFeedbarParams struct {
	Id          int64        `json:"id"`
	DisplayTime int64        `json:"display_time"`
	Content     string       `json:"content"`
	ContentEN   string       `json:"content_en"`
	Type        def.EnumType `json:"type"`
	Sort        int64        `json:"sort"`
	Url         string       `json:"url"`
	PicUrl      string       `json:"pic_url"`
}

func UpdateFeedbar(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params updateFeedbarParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	displayTime := params.DisplayTime
	content := params.Content
	contentEN := params.ContentEN
	tType := params.Type
	url := params.Url
	picUrl := params.PicUrl
	sort := params.Sort

	// 参数check
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "id params error")
	}
	if displayTime <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "display_time params error")
	}
	if !checkFeedbarType(tType) {
		return helpers.FailReturn(helpers.LogicErrorCode, "type params error")
	}

	contentLen := len([]rune(content))
	if contentLen <= 0 || contentLen > ContentLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "content params error")
	}
	contentENLen := len([]rune(contentEN))
	if contentENLen <= 0 || contentENLen > ContentLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "content_en params error")
	}
	if len([]rune(url)) > UrlLenLimit {
		return helpers.FailReturn(helpers.LogicErrorCode, "url params error")
	}
	if sort <= 0 || sort > 100 {
		sort = 100
	}

	// 添加数据
	feedbarM := models.Feedbar{}
	feedbarInfo, err := feedbarM.GetById(ctx, params.Id)
	if err != nil || feedbarInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	dateT := time.Unix(displayTime, 0).Format("2006-01-02 15:04:05")
	formatTime, _ := time.Parse("2006-01-02 15:04:05", dateT)
	feedbarInfo.DisplayTime = formatTime
	feedbarInfo.Content = content
	feedbarInfo.ContentEN = contentEN
	feedbarInfo.Type = tType
	feedbarInfo.Url = url
	feedbarInfo.PicUrl = picUrl
	feedbarInfo.Sort = sort
	err = feedbarInfo.Save(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}

// 设置动态栏
type setFeedbarParams struct {
	Id          def.AutoIDType `json:"id"`
	OperateType string         `json:"operate_type"`
}

func SetFeedbar(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params setFeedbarParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	operateType := params.OperateType

	// 参数check
	if !checkFeedbarOperateType(operateType) {
		return helpers.FailReturn(helpers.ParamErrorCode, "operate_type params error")
	}
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "id params error")
	}

	feedbarM := models.Feedbar{}
	feedbarInfo, err := feedbarM.GetById(ctx, params.Id)
	if err != nil || feedbarInfo.ID <= 0 {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	switch operateType {
	case OperateTypePublish:
		feedbarInfo.Status = models.StatusOnline
		break
	case OperateTypeWithdraw:
		feedbarInfo.Status = models.StatusNew
		break
	case OperateTypeTop:
		feedbarInfo.IsTop = 1
		_ = feedbarM.BatchUpdateByTop(ctx, 0)
		break
	case OperateTypeUnTop:
		feedbarInfo.IsTop = 0
		break
	case OperateTypeHome:
		feedbarInfo.IsHome = 1
		break
	case OperateTypeUnhome:
		feedbarInfo.IsHome = 0
		break
	}
	err = feedbarInfo.Update(ctx)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}
