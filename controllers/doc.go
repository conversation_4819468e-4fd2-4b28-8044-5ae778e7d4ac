package controllers

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// 获取任务信息
type listPicParams struct {
	Limit   int    `json:"limit"`
	Page    int    `json:"page"`
	Keyword string `json:"keyword"`
}

func ListPic(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params listPicParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	keyword := params.Keyword

	// 参数check
	limit, page := formatPaging(params.Limit, params.Page)

	// 获取用户信息
	picM := models.DocPic{}
	total, err := picM.CountByCond(ctx, keyword)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	taskList, err := picM.GetList(ctx, keyword, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	// 数据组装
	var items []any
	for _, task := range taskList {
		items = append(items, task.SwapData())
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(result)
}

// 添加addParams
type addPicParams struct {
	FileUrl string `json:"file_url"`
	Remark  string `json:"remark"`
}

func AddPic(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params addPicParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	fileUrl := helpers.Trim(params.FileUrl)
	remark := helpers.Trim(params.Remark)

	// 参数检验
	remarkLen := len(remark)
	if remarkLen > DescLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "remark params error")
	}

	fileUrlLen := len(fileUrl)
	if fileUrlLen <= 0 || fileUrlLen > UrlLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "file_url params error")
	}

	picNew := models.DocPic{
		FileUrl: fileUrl,
		Remark:  remark,
	}
	_, err := picNew.Add(ctx, picNew)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(nil)
}

// 添加editTagParams
type editPicParams struct {
	Id      int64  `json:"id"`
	Remark  string `json:"remark"`
	Operate string `json:"operate"`
}

func SetPic(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params editPicParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	remark := helpers.Trim(params.Remark)
	operate := helpers.Trim(params.Operate)

	remarkLen := len(remark)
	if remarkLen > DescLenLimit {
		return helpers.FailReturn(helpers.ParamErrorCode, "remark params error")
	}
	if params.Id <= 0 {
		return helpers.FailReturn(helpers.ParamErrorCode, "id params error")
	}
	if operate != "del" && operate != "update" {
		return helpers.FailReturn(helpers.ParamErrorCode, "operate params error")
	}

	picM := models.DocPic{}
	picInfo, err := picM.GetById(ctx, params.Id)
	if err != nil {
		return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
	}

	if picInfo.ID > 0 {
		if operate == "del" {
			picInfo.Status = models.StatusDel
		} else {
			picInfo.Remark = remark
		}

		err = picInfo.Save(ctx)
		if err != nil {
			return helpers.FailReturn(helpers.LogicErrorCode, "data error, please repeat")
		}
	}

	return helpers.SuccReturn(nil)
}
