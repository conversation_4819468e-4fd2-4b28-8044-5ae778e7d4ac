package controllers

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

// 获取任务信息
type taskListParams struct {
	Limit       int            `json:"limit"`
	Page        int            `json:"page"`
	TypeList    []def.EnumType `json:"type_list"`
	UserKey     string         `json:"user_keyword"`
	Keyword     string         `json:"keyword"`
	ProjectId   def.AutoIDType `json:"project_id"`
	SubmitWay   int            `json:"submit_way"` // 提交方式，-1:所有 0:web 1:api
	MinTaskTime int64          `json:"min_task_time"`
	MaxTaskTime int64          `json:"max_task_time"`
}

func ListTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params taskListParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)

	minTaskTime := params.MinTaskTime
	maxTaskTime := params.MaxTaskTime
	keyword := helpers.Trim(params.Keyword)
	userKey := helpers.Trim(params.UserKey)
	if maxTaskTime > 0 {
		maxTaskTime = maxTaskTime + 86400
	}

	// 参数check
	//limit, page := formatPaging(params.Limit, params.Page)
	limit, page := params.Limit, params.Page

	// 检索用户
	var userIdList []int64
	if userKey != "" {
		userCond := models.UserCond{
			Keyword: []string{userKey},
		}
		userM := models.User{}
		userList, _ := userM.GetList(ctx, userCond, 100, 1)
		for _, user := range userList {
			userIdList = append(userIdList, int64(user.ID))
		}
		if len(userIdList) == 0 {
			return helpers.SuccReturn(nil)
		}
	}

	// 获取任务信息
	cond := models.TaskCond{
		UserIdList:       userIdList,
		FilterUserIdList: getFilterUserId(ctx),
		TypeList:         params.TypeList,
		Keyword:          keyword,
		ProjectId:        params.ProjectId,
		GtCreatedAt:      helpers.UnixToTimeStr(minTaskTime),
		LtCreatedAt:      helpers.UnixToTimeStr(maxTaskTime),
	}

	switch params.SubmitWay {
	case -1:
		cond.IsAPI = -1
	case 0:
		cond.IsAPI = 0
	case 1:
		cond.IsAPI = 1
	}

	taskM := models.Task{}
	total, err := taskM.CountByCond(ctx, cond)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	userNum, err := taskM.CountDistinctByCond(ctx, cond, "user_id")
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}
	taskList, err := taskM.GetByCond(ctx, cond, limit, page)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	var userIdArr []int64
	for _, task := range taskList {
		userIdArr = append(userIdArr, int64(task.UserId))
	}
	userMap := getUserMap(ctx, userIdArr)

	// 数据组装
	var items []any
	for _, task := range taskList {
		tmp := task.SwapData()
		tmp["username"] = ""
		tmp["user_type"] = 0
		tmp["user_remark"] = ""
		if user, ok := userMap[int(task.UserId)]; ok {
			tmp["username"] = user.Username
			tmp["user_type"] = user.Type
			tmp["user_remark"] = user.Remark
		}

		items = append(items, tmp)
	}

	result := map[string]any{
		"items":    items,
		"total":    total,
		"user_num": userNum,
	}
	return helpers.SuccReturn(result)
}

type rudderGetParams struct {
	TaskId int64 `json:"task_id"`
}

func GetRudderInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	var params rudderGetParams
	_ = json.Unmarshal(ctx.Value("params").([]byte), &params)
	taskId := params.TaskId

	taskM := models.Task{}
	taskInfo, err := taskM.GetTaskById(ctx, taskId)
	if err != nil {
		return helpers.FailReturn(helpers.DBErrorCode, "data error, please repeat")
	}

	result := taskInfo.SwapData()
	result["rudder_id"] = taskInfo.ServerTaskId
	return helpers.SuccReturn(result)
}
