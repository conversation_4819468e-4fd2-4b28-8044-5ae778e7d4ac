package httpapi

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
)

// RecoverMiddleWareFunc 全局错误捕捉中间件
func RecoverMiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		defer func() {
			if err := recover(); err != nil {
				trace := make([]byte, 4096)
				runtime.Stack(trace[:], false)
				title := fmt.Sprintf("panic:%v", err)

				// 堆栈
				traceStr := strings.ReplaceAll(string(trace), "\n", "------")

				// 未登录（不打印日志、不发送通知）
				checkResp := strings.Contains(title, "get admin info failed")
				if !checkResp {
					logFields := []logit.Field{
						logit.String("panic_trace", traceStr),
					}
					logger.Error(ctx, title, logFields...)
					go helpers.HelixNotice(ctx, title)
				}

				// json 返回给请求方
				var resp ghttp.Response
				resp, ok := err.(ghttp.Response)
				if !ok {
					resp = helpers.FailReturn(helpers.CommonErrorCode, "data error, please repeat")
				}

				_ = resp.WriteTo(w)
				return
			}
		}()

		return next.Next(ctx, w, req)
	}
}

// AuthMiddleWareFunc 用户验证中间件
func AuthMiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	if logger == nil {
		panic("logger is nil")
	}
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		// 登录验证
		cookie, ok := req.Cookie("HELIXUSS")
		if !ok {
			panic(helpers.FailReturn(helpers.LoginErrorCode, "no login，get admin info failed"))
		}

		// 查询登陆信息
		cacheKey := redis.AdminTokenPrefix + cookie.Value
		adminIdStr := redis.Get(ctx, cacheKey)
		adminId, _ := strconv.ParseInt(adminIdStr, 10, 64)
		if adminId <= 0 {
			panic(helpers.FailReturn(helpers.LoginErrorCode, "login expire，get admin info failed"))
		}

		// 获取用户信息(先读缓存)
		var adminInfo models.AdminInfo
		adminCacheKey := redis.AdminInfoPrefix + strconv.Itoa(int(adminId))
		adminInfoStr := redis.Get(ctx, adminCacheKey)
		_ = json.Unmarshal([]byte(adminInfoStr), &adminInfo)

		// 判断缓存是否过期
		if adminInfo.AdminId <= 0 {
			adminM := models.Admin{}
			adminR, err := adminM.GetById(ctx, adminId)
			if err != nil || adminR.ID <= 0 {
				panic(helpers.FailReturn(helpers.LoginErrorCode, "get admin info failed, please repeat"))
			}

			roleM := models.Role{}
			roleInfo, err := roleM.GetById(ctx, adminR.RoleId)
			if err != nil || roleInfo.ID <= 0 {
				panic(helpers.FailReturn(helpers.LoginErrorCode, "get admin info failed, please repeat"))
			}

			var permissionIdList []def.AutoIDType
			_ = json.Unmarshal([]byte(adminR.PermissionIds), &permissionIdList)

			// 获取permission uri
			permissionM := models.Permission{}
			permissionList, err := permissionM.GetByIds(ctx, permissionIdList)
			if err != nil {
				panic(helpers.FailReturn(helpers.LoginErrorCode, "get admin info failed, please repeat"))
			}
			var permissionUriList []string
			for _, item := range permissionList {
				permissionUriList = append(permissionUriList, item.Uri)
			}

			adminInfo = models.AdminInfo{
				AdminId:           adminId,
				Username:          adminR.Username,
				RoleAlias:         roleInfo.Alias,
				RoleId:            adminR.RoleId,
				PermissionIds:     permissionIdList,
				PermissionUriList: permissionUriList,
			}

			// 写入redis
			adminByte, _ := json.Marshal(adminInfo)
			redis.Set(ctx, adminCacheKey, string(adminByte), 7200)
		}

		newCtx := context.WithValue(ctx, "admin_info", adminInfo)
		return next.Next(newCtx, w, req)
	}
}

// 参数处理
func ParamsMiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	if logger == nil {
		panic("logger is nil")
	}
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		// 请求参数写日志
		messageByte, _ := json.Marshal(helpers.GetBodyParams(req.Body()))
		logger.Notice(ctx, string(messageByte))

		newCtx := context.WithValue(ctx, "params", messageByte)
		return next.Next(newCtx, w, req)
	}
}

// 权限处理
func PermissionMiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	if logger == nil {
		panic("logger is nil")
	}
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		adminInfo := ctx.Value("admin_info").(models.AdminInfo)
		bodyStr := string(ctx.Value("params").([]byte))

		// 权限校验
		uri := req.RequestURI()
		endIndex := strings.Index(uri, "?")
		if endIndex > 0 {
			uri = uri[0:endIndex]
		}

		if !checkCommonUri(uri) && adminInfo.RoleAlias != models.AliasSuper {
			permissionM := models.Permission{}
			permissionInfo, err := permissionM.GetByUri(ctx, uri)
			if err != nil || permissionInfo.ID <= 0 {
				panic("permission is invalid")
			}

			checkRes := helpers.Contain(adminInfo.PermissionIds, permissionInfo.ID)
			if !checkRes {
				panic("permission is invalid")
			}
		}

		// 请求记录
		actionLogM := models.ActionLog{}
		actionLogNew := models.ActionLog{
			Uri:     uri,
			Params:  bodyStr,
			AdminId: adminInfo.AdminId,
		}
		_, err := actionLogM.Add(ctx, actionLogNew)
		if err != nil {
			panic("action log fail, please repeat")
		}

		return next.Next(ctx, w, req)
	}
}

// 通用权限排除
var CommonUriList = map[string]bool{
	"/admin/info":      true,
	"/admin/menu":      true,
	"/password/update": true,
	"/bos/auth":        true,
	"/bos/url":         true,
	"/tag/list":        true,
	"/dataset/export":  true,
	"/user/download":   true,
	"/get/rudder":      true,
	"/platform/data":   true,
	"/dot/data/count":  true,
	"/dot/data/csv":    true,
	"/dot/data/list":   true,
}

func checkCommonUri(uri string) bool {
	_, ok := CommonUriList[uri]
	return ok
}
