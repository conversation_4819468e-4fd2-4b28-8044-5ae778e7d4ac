package httpapi

import (
	"context"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/ghttp/pprof"
	"icode.baidu.com/helix_web/controllers"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

// Router 获取web路由
func Router(ser *ghttp.DefaultServer) ghttp.Router {
	logger := resource.LoggerService

	// 若http server 内部出现异常，将通过此logger打印日志
	ghttp.DefaultLogger = logger

	router := ghttp.NewRouter()
	router.SetLogger(logger)

	if ser.WriteTimeout > 0 {
		// 若server有设置WriteTimeout，则整体加上超时控制
		// 1.日志中可以打印出由于server超时导致的504异常
		// 2.业务逻辑可以更及时的终止运行
		router.Use(ghttp.NewTimeoutMiddleWareFunc(ser.WriteTimeout, nil))
	}

	// 注册日志中间件，初始化日志功能，打印访问日志(access_log)
	// 若需要对日志字段进行调整，请修改这里
	router.Use(ghttp.NewLogMiddleWareFunc(logger, ghttp.DefaultServerLogFields))

	// 注册panic Recover中间件，可以处理handlerFunc里 出现的panic
	// 需要注意的是，若是使用go xxx()自己新开启的协程，是不能recover的
	router.Use(RecoverMiddleWareFunc(logger))

	// 参数处理
	router.Use(ParamsMiddleWareFunc(logger))

	// 监控
	registerPprof(router)

	// 不需要验证的路由
	router.HandleFunc("any", "/", func(ctx context.Context, req ghttp.Request) ghttp.Response {
		return helpers.SuccReturn(nil)
	})
	router.HandleFunc("post", "/admin/login", controllers.Login)

	// 需要验证登录的路
	authRouter := router.Group("", AuthMiddleWareFunc(logger), PermissionMiddleWareFunc(logger))
	authRouter.HandleFunc("post", "/admin/info", controllers.GetAdminInfo)
	authRouter.HandleFunc("post", "/admin/menu", controllers.GetAdminMenu)
	authRouter.HandleFunc("post", "/password/update", controllers.UpdatePassport)
	authRouter.HandleFunc("post", "/bos/auth", controllers.GetBosAuth)
	authRouter.HandleFunc("post", "/bos/url", controllers.GetBosUrl)

	authRouter.HandleFunc("post", "/feedbar/list", controllers.GetFeedbarList)
	authRouter.HandleFunc("post", "/feedbar/add", controllers.AddFeedbar)
	authRouter.HandleFunc("post", "/feedbar/update", controllers.UpdateFeedbar)
	authRouter.HandleFunc("post", "/feedbar/set", controllers.SetFeedbar)

	authRouter.HandleFunc("post", "/achievement/list", controllers.GetAchievementList)
	authRouter.HandleFunc("post", "/achievement/add", controllers.AddAchievement)
	authRouter.HandleFunc("post", "/achievement/update", controllers.UpdateAchievement)
	authRouter.HandleFunc("post", "/achievement/set", controllers.SetAchievement)

	authRouter.HandleFunc("post", "/example/list", controllers.GetSerialList)
	authRouter.HandleFunc("post", "/example/add", controllers.AddSerial)
	authRouter.HandleFunc("post", "/example/update", controllers.UpdateSerial)
	authRouter.HandleFunc("post", "/example/set", controllers.SetSerial)

	// 超级管理
	authRouter.HandleFunc("post", "/role/list", controllers.GetRoleList)
	authRouter.HandleFunc("post", "/admin/list", controllers.GetAdminList)
	authRouter.HandleFunc("post", "/admin/add", controllers.AddAdmin)
	authRouter.HandleFunc("post", "/admin/del", controllers.DelAdmin)
	authRouter.HandleFunc("post", "/update/admin", controllers.UpdateAdmin)
	authRouter.HandleFunc("post", "/reset/admin/passport", controllers.ResetAdminPassport)

	// 数据集
	authRouter.HandleFunc("post", "/dataset/statistics", controllers.GetDatasetStatistics)
	authRouter.HandleFunc("post", "/dataset/list", controllers.GetDatasetList)
	authRouter.HandleFunc("post", "/dataset/info", controllers.GetDatasetInfo)
	authRouter.HandleFunc("post", "/dataset/history", controllers.GetDatasetHistory)
	authRouter.HandleFunc("post", "/dataset/collect", controllers.CollectDataset)
	authRouter.HandleFunc("post", "/dataset/add", controllers.AddDataset)
	authRouter.HandleFunc("post", "/dataset/edit", controllers.EditDataset)
	authRouter.HandleFunc("post", "/dataset/log", controllers.GetDatasetLog)
	authRouter.HandleFunc("post", "/dataset/url", controllers.GetDatasetUrl)
	authRouter.HandleFunc("post", "/dataset/export", controllers.GetDatasetExport)

	// 标签
	authRouter.HandleFunc("post", "/tag/list", controllers.GetTagList)
	authRouter.HandleFunc("post", "/tag/add", controllers.AddTag)
	authRouter.HandleFunc("post", "/tag/edit", controllers.EditTag)
	authRouter.HandleFunc("post", "/tag/del", controllers.DelTag)

	// 用户相关
	authRouter.HandleFunc("post", "/subscribe/list", controllers.ListSubscribe)
	authRouter.HandleFunc("post", "/user/list", controllers.ListUser)
	authRouter.HandleFunc("post", "/user/edit", controllers.EditUser)
	authRouter.HandleFunc("post", "/user/change", controllers.ChangeUser)
	authRouter.HandleFunc("post", "/user/del", controllers.DelUser)
	authRouter.HandleFunc("post", "/trial/add", controllers.AddTrial)
	authRouter.HandleFunc("post", "/trial/download", controllers.DownloadTrial)
	authRouter.HandleFunc("post", "/trial/set", controllers.SetTrial)
	authRouter.HandleFunc("post", "/user/download", controllers.DownloadUser)
	authRouter.HandleFunc("post", "/feedback/list", controllers.ListFeedback)
	authRouter.HandleFunc("post", "/feedback/update", controllers.UpdateFeedback)
	authRouter.HandleFunc("post", "/coupon/send", controllers.AddCoupon)
	authRouter.HandleFunc("post", "/coupon/set", controllers.SetCoupon)
	authRouter.HandleFunc("post", "/voucher", controllers.AddVoucher)
	authRouter.HandleFunc("put", "/voucher", controllers.SetVoucher)
	authRouter.HandleFunc("put", "/user/priority", controllers.ChangeUserPriority)

	// 任务相关
	authRouter.HandleFunc("post", "/task/list", controllers.ListTask)
	authRouter.HandleFunc("post", "/bill/list", controllers.ListBill)
	authRouter.HandleFunc("post", "/bill/push", controllers.PushBilling)
	authRouter.HandleFunc("post", "/doc/image/list", controllers.ListPic)
	authRouter.HandleFunc("post", "/doc/image/set", controllers.SetPic)
	authRouter.HandleFunc("post", "/doc/image/add", controllers.AddPic)
	authRouter.HandleFunc("post", "/get/rudder", controllers.GetRudderInfo)

	// 用户反馈
	authRouter.HandleFunc("post", "/portrait/list", controllers.ListPortrait)
	authRouter.HandleFunc("post", "/comment/list", controllers.ListComment)
	authRouter.HandleFunc("post", "/user/bill", controllers.GetUserBill)

	// 平台数据
	authRouter.HandleFunc("post", "/platform/data", controllers.GetPlatformData)
	authRouter.HandleFunc("post", "/dot/data/count", controllers.DotCount)
	authRouter.HandleFunc("post", "/dot/data/csv", controllers.DotCountCSV)
	authRouter.HandleFunc("post", "/dot/data/list", controllers.DotList)

	// 3.0 管理
	authRouter.HandleFunc("post", "/overall/add", controllers.AddOverall)
	authRouter.HandleFunc("post", "/overall/list", controllers.ListOverall)
	authRouter.HandleFunc("post", "/skill/list", controllers.ListSkill)
	authRouter.HandleFunc("post", "/skill/add", controllers.AddSkill)
	authRouter.HandleFunc("post", "/skill/update", controllers.UpdateSkill)
	authRouter.HandleFunc("post", "/skill/set", controllers.SetSkill)
	authRouter.HandleFunc("post", "/cooperation/list", controllers.ListCoop)
	authRouter.HandleFunc("post", "/cooperation/update", controllers.UpdateCoop)

	// 数据管理
	authRouter.HandleFunc("post", "/white/add", controllers.AddWhite)
	authRouter.HandleFunc("post", "/white/del", controllers.DelWhite)
	authRouter.HandleFunc("post", "/white/set", controllers.SetWhite)
	authRouter.HandleFunc("post", "/white/list", controllers.ListWhite)

	// 项目管理
	authRouter.HandleFunc("post", "/project/list", controllers.ListProject)
	authRouter.HandleFunc("post", "/bulletin/list", controllers.GetBulletinList)
	authRouter.HandleFunc("post", "/bulletin/create", controllers.CreateBulletin)
	authRouter.HandleFunc("post", "/bulletin/set", controllers.SetBulletin)

	return router
}

// registerPprof 注册/debug/pprof 的handler 用于性能分析
// 引入的pprof，会给标准库的DefaultServeMux 都注册上/debug/xxx系统的handler
// 开启http pprof, 该功能依赖server 配置一个比较大的WriteTimeout
// 需要 WriteTimeout > 30s
func registerPprof(router ghttp.Router) {
	if env.RunMode() == env.RunModeRelease {
		return
	}

	// 访问地址 http://ip:port/debug/pprof/
	pprof.RegisterPProf(router, ghttp.NewInternalIPMiddleWareFunc())
}
