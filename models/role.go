package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

// 表结构
type Role struct {
	ID        def.AutoIDType `gorm:"id"`
	Name      string         `gorm:"name"`
	<PERSON><PERSON>     string         `gorm:"alias"`
	MenuIds   string         `gorm:"menu_ids"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

const (
	AliasSuper    = "super"
	AliasPlatform = "platform"
	AliasDBManage = "db_manage"
	AliasDB       = "db"
)

func (r *Role) TableName() string {
	return "manage_role"
}

// 添加 role
func (r *Role) Add(ctx context.Context, roleNew Role) (Role, error) {
	roleNew.CreatedAt = time.Now()
	roleNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&roleNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return roleNew, err
}

// 通过id获取
func (r *Role) GetById(ctx context.Context, id def.AutoIDType) (Role, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	roleData := Role{}
	err := db.Where(condWhere).First(&roleData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return roleData, err
	}
	return roleData, nil
}

// 获取所有数据
func (r *Role) GetList(ctx context.Context, aliasList []string) ([]Role, error) {
	db := resource.Gorm.WithContext(ctx)

	condWhere := map[string]any{
		"alias": aliasList,
	}

	// 执行查询
	var roleList []Role
	err := db.Where(condWhere).Order("id asc").Find(&roleList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return roleList, err
}

// 数据转化
func (r *Role) SwapData() map[string]any {
	var roleData map[string]any
	if r.ID <= 0 {
		return roleData
	}

	var menuIdList []def.AutoIDType
	_ = json.Unmarshal([]byte(r.MenuIds), &menuIdList)

	roleData = map[string]any{
		"id":         r.ID,
		"name":       r.Name,
		"alias":      r.Alias,
		"menu_ids":   menuIdList,
		"created_at": r.CreatedAt.Unix(),
		"updated_at": r.UpdatedAt.Unix(),
	}
	return roleData
}
