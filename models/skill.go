package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	SkillTypeXFZ   def.EnumType = 10
	SkillTypeDB    def.EnumType = 20
	SkillTypeDBXFZ def.EnumType = 30
	SkillTypeDBDB  def.EnumType = 40
	SkillTypeRNA   def.EnumType = 50

	SkillStatus def.EnumType = 20
)

var SkillTypeMap = map[def.EnumType]bool{
	SkillTypeXFZ:   true,
	SkillTypeDB:    true,
	SkillTypeDBXFZ: true,
	SkillTypeDBDB:  true,
	SkillTypeRNA:   true,
}

type Skill struct {
	ID        def.AutoIDType `gorm:"id"`
	AdminId   def.AutoIDType `gorm:"admin_id"`
	Type      def.EnumType   `gorm:"type"`
	Sort      def.CountType  `gorm:"type"`
	Status    def.EnumType   `gorm:"status"`
	Data      string         `gorm:"data"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (s *Skill) TableName() string {
	return "helix_skill"
}

// GetById 通过Id获取信息
func (s *Skill) GetById(ctx context.Context, id def.AutoIDType) (Skill, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	SkillData := Skill{}
	err := db.Where(condWhere).First(&SkillData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return SkillData, err
	}
	return SkillData, nil
}

func (s *Skill) GetByType(ctx context.Context, tType def.EnumType) ([]Skill, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)
	if tType > 0 {
		db = db.Where("type = ?", tType)
	}

	// 执行查询
	var skillList []Skill
	err := db.Order("sort asc, created_at desc").Find(&skillList).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return skillList, err
	}
	return skillList, nil
}

// 添加 Skill
func (s *Skill) Add(ctx context.Context, skillNew Skill) (Skill, error) {
	skillNew.Status = StatusNew
	skillNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&skillNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return skillNew, err
}

// 保存、修改
func (s *Skill) Save(ctx context.Context) error {
	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(s).Updates(&s).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// SwapData 数据转化
func (s *Skill) SwapData() map[string]any {
	var data map[string]any
	if s.ID <= 0 {
		return data
	}

	var content any
	_ = json.Unmarshal([]byte(s.Data), &content)

	data = map[string]any{
		"id":         s.ID,
		"admin_id":   s.AdminId,
		"type":       s.Type,
		"sort":       s.Sort,
		"status":     s.Status,
		"data":       content,
		"created_at": s.CreatedAt.Unix(),
		"updated_at": s.UpdatedAt.Unix(),
	}
	return data
}
