package models

import (
	"context"
	"errors"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	VoucherStatusNormal   = 0 // 正常
	VoucherStatusDisabled = 1 // 停用
	VoucherStatusDeleted  = 2 // 删除

	voucherListLimit = 50

	VoucherListOrderDesc = "DESC"
	VoucherListOrderAsc  = "ASC"
)

// 表结构
type Voucher struct {
	ID         uint64    `gorm:"id"`
	UserID     int64     `gorm:"user_id"`
	AdminID    int64     `gorm:"admin_id"`
	TaskType   int       `gorm:"task_type"`
	Amount     float64   `gorm:"amount"`
	RestAmount float64   `gorm:"rest_amount"`
	Status     int       `gorm:"status"`
	Remark     string    `gorm:"remark"`
	StartTime  time.Time `gorm:"start_time"`
	EndTime    time.Time `gorm:"login_time"`
	CreatedAt  time.Time `gorm:"created_at"`
	UpdatedAt  time.Time `gorm:"updated_at"`
}

type VoucherSimpleInfo struct {
	VoucherID uint64  `json:"voucher_id"`
	Amount    float64 `json:"amount"`
}

func (c *Voucher) TableName() string {
	return "helix_voucher"
}

type VoucherGetListParam struct {
	UserID          int64
	TaskType        []def.EnumType
	Status          []def.EnumType
	RestAmountRange []float64
	StartTimeRange  []time.Time
	EndTimeRange    []time.Time
	Page            int
	Limit           int
	OrderBy         string
	Order           string
}

type VoucherCountParam = VoucherGetListParam

type VoucherReturnParam = []*VoucherSimpleInfo

func (p *VoucherGetListParam) Valid() bool {
	if p.Page == 0 {
		p.Page = 1
	}
	if p.Limit == 0 {
		p.Limit = voucherListLimit
	}
	if p.OrderBy != "" && p.Order == "" {
		p.Order = VoucherListOrderAsc
	}
	if p.Order != "" && p.Order != VoucherListOrderAsc && p.Order != VoucherListOrderDesc {
		return false
	}
	return (len(p.RestAmountRange) == 0 || len(p.RestAmountRange) == 2) &&
		(len(p.StartTimeRange) == 0 || len(p.StartTimeRange) == 2) &&
		(len(p.EndTimeRange) == 0 || len(p.EndTimeRange) == 2)
}

func (c *Voucher) Add(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	if err := db.Create(c).Error; err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return err
	}
	return nil
}

func (c *Voucher) GetByID(ctx context.Context, voucherID int64) (*Voucher, error) {
	db := resource.Gorm.WithContext(ctx)
	// 执行查询
	var voucher Voucher
	if err := db.Where("id = ?", voucherID).Find(&voucher).Error; err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return nil, err
	}
	return &voucher, nil
}

// GetList 获取点券列表
// ctx context.Context 上下文对象
// userID int64 用户ID
// taskType int64 任务类型
// 返回值 []Voucher 点券列表，error 错误信息
func (c *Voucher) GetList(ctx context.Context, param *VoucherGetListParam) ([]Voucher, error) {
	if !param.Valid() {
		helpers.LogError(ctx, errors.New("DB Voucher GetList 参数错误"))
		go helpers.HelixNotice(ctx, "DB Voucher GetList 参数错误")
		return nil, errors.New("DB Voucher GetList 参数错误")
	}
	db := resource.Gorm.WithContext(ctx)
	// 拼接where
	if param.UserID != 0 {
		db = db.Where("user_id = ?", param.UserID)
	}
	if len(param.TaskType) != 0 {
		db = db.Where("task_type in (?)", param.TaskType)
	}
	if len(param.Status) > 0 {
		db = db.Where("status IN (?)", param.Status)
	}
	if len(param.RestAmountRange) > 0 {
		db = db.Where("rest_amount > ?", param.RestAmountRange[0])
		db = db.Where("rest_amount <= ?", param.RestAmountRange[1])
	}
	if len(param.StartTimeRange) > 0 {
		db = db.Where("start_time > ?", param.StartTimeRange[0])
		db = db.Where("start_time <= ?", param.StartTimeRange[1])
	}
	if len(param.EndTimeRange) > 0 {
		db = db.Where("end_time > ?", param.EndTimeRange[0])
		db = db.Where("end_time <= ?", param.EndTimeRange[1])
	}
	if param.OrderBy != "" {
		order := param.OrderBy
		if param.Order == "" {
			order += " " + "ASC"
		} else {
			order += " " + param.Order
		}
		db = db.Order(order)
	}
	db = db.Offset((param.Page - 1) * param.Limit).Limit(param.Limit)
	// 执行查询
	var voucherList []Voucher
	if err := db.Find(&voucherList).Error; err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return nil, err
	}
	return voucherList, nil
}

func (c *Voucher) Count(ctx context.Context, param *VoucherCountParam) (int64, error) {
	if !param.Valid() {
		helpers.LogError(ctx, errors.New("DB Voucher Count 参数错误"))
		go helpers.HelixNotice(ctx, "DB Voucher Count 参数错误")
		return 0, errors.New("DB Voucher Count 参数错误")
	}
	db := resource.Gorm.WithContext(ctx)
	// 拼接where
	if param.UserID != 0 {
		db = db.Where("user_id = ?", param.UserID)
	}
	if len(param.TaskType) != 0 {
		db = db.Where("task_type IN (?)", param.TaskType)
	}
	if len(param.Status) > 0 {
		db = db.Where("status IN (?)", param.Status)
	}
	if len(param.RestAmountRange) > 0 {
		db = db.Where("rest_amount > ?", param.RestAmountRange[0])
		db = db.Where("rest_amount <= ?", param.RestAmountRange[1])
	}
	if len(param.StartTimeRange) > 0 {
		db = db.Where("start_time > ?", param.StartTimeRange[0])
		db = db.Where("start_time <= ?", param.StartTimeRange[1])
	}
	if len(param.EndTimeRange) > 0 {
		db = db.Where("end_time > ?", param.EndTimeRange[0])
		db = db.Where("end_time <= ?", param.EndTimeRange[1])
	}
	// 执行查询
	var count int64
	err := db.Model(c).Count(&count).Error
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 修改点券
func (c *Voucher) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(c).Updates(&c).Error
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return err
}

// 数据转化
func (c *Voucher) SwapData() map[string]interface{} {
	var voucherData map[string]interface{}
	if c.ID <= 0 {
		return voucherData
	}

	voucherData = map[string]interface{}{
		"id":          c.ID,
		"user_id":     c.UserID,
		"admin_id":    c.AdminID,
		"task_type":   c.TaskType,
		"amount":      c.Amount,
		"rest_amount": c.RestAmount,
		"status":      c.Status,
		"start_time":  c.StartTime.Unix(),
		"end_time":    c.EndTime.Unix(),
	}
	return voucherData
}
