package models

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const CouponStatusLose = 1

// 表结构
type Coupon struct {
	ID         def.AutoIDType `gorm:"id"`
	UserId     def.AutoIDType `gorm:"user_id"`
	AdminId    def.AutoIDType `gorm:"admin_id"`
	TaskType   def.EnumType   `gorm:"task_type"`
	RangeList  string         `gorm:"range_list"`
	Amount     float64        `gorm:"amount"`
	RestAmount float64        `gorm:"rest_amount"`
	Status     def.EnumType   `gorm:"status"`
	Remark     string         `gorm:"remark"`
	StartTime  time.Time      `gorm:"start_time"`
	EndTime    time.Time      `gorm:"login_time"`
	CreatedAt  time.Time      `gorm:"created_at"`
	UpdatedAt  time.Time      `gorm:"updated_at"`
}

func (c *Coupon) TableName() string {
	return "helix_coupon"
}

func (c *Coupon) GetListByTaskType(ctx context.Context, userId def.AutoIDType, taskType def.EnumType) ([]Coupon, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status = ?", StatusNew)
	db = db.Where("user_id = ?", userId)
	db = db.Where("task_type = ?", taskType)

	// 执行查询
	var couponList []Coupon
	err := db.Find(&couponList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())

		return couponList, err
	}

	return couponList, nil
}

func (c *Coupon) GetList(ctx context.Context, userID def.AutoIDType, taskType []def.EnumType,
	status []def.EnumType, limit, page def.IntType, orderBy ...string) ([]Coupon, error) {
	db := resource.Gorm.WithContext(ctx)
	if userID > 0 {
		db = db.Where("user_id = ?", userID)
	}
	if len(status) > 0 {
		db = db.Where("status in ?", status)
	}
	if len(taskType) > 0 {
		db = db.Where("task_type in ?", taskType)
	}

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var couponList []Coupon
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&couponList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())

		return couponList, err
	}

	return couponList, nil
}

func (c *Coupon) GetById(ctx context.Context, couponId def.AutoIDType) (Coupon, error) {
	db := resource.Gorm.WithContext(ctx)
	condWhere := map[string]any{
		"id": couponId,
	}

	// 执行查询
	couponData := Coupon{}
	err := db.Where(condWhere).First(&couponData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return couponData, err
	}
	return couponData, nil
}

func (c *Coupon) Add(ctx context.Context, couponNew Coupon) (Coupon, error) {
	couponNew.Status = StatusNew
	couponNew.CreatedAt = time.Now()
	couponNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&couponNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return couponNew, err
}

// 修改试用数据
func (c *Coupon) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(c).Updates(&c).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (c *Coupon) SwapData() map[string]any {
	var couponData map[string]any
	if c.ID <= 0 {
		return couponData
	}

	var rangeList []def.EnumType
	_ = json.Unmarshal([]byte(c.RangeList), &rangeList)

	couponData = map[string]any{
		"id":          c.ID,
		"user_id":     c.UserId,
		"admin_id":    c.AdminId,
		"task_type":   c.TaskType,
		"range_list":  rangeList,
		"amount":      c.Amount,
		"rest_amount": c.RestAmount,
		"status":      c.Status,
		"start_time":  SwapFieldUnix(c.StartTime.Unix()),
		"end_time":    SwapFieldUnix(c.EndTime.Unix()),
		"created_at":  c.CreatedAt.Unix(),
		"updated_at":  c.UpdatedAt.Unix(),
	}
	return couponData
}
