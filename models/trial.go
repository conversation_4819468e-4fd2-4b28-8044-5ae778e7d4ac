package models

import (
	"context"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	TrialTypeTime def.EnumType = 10
	TrialTypeNum  def.EnumType = 20

	TrialStatusLose def.EnumType = 1
)

// 表结构
type Trial struct {
	ID        def.AutoIDType `gorm:"id"`
	UserId    def.AutoIDType `gorm:"user_id"`
	Type      def.EnumType   `gorm:"type"`
	UserType  def.EnumType   `gorm:"user_type"`
	TaskType  def.EnumType   `gorm:"task_type"`
	UsedNum   def.CountType  `gorm:"used_num"`
	LimitNum  def.CountType  `gorm:"limit_num"`
	Status    def.EnumType   `gorm:"status"`
	StartTime time.Time      `gorm:"start_time"`
	EndTime   time.Time      `gorm:"end_time"`
	AdminId   def.AutoIDType `gorm:"admin_id"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (t *Trial) TableName() string {
	return "helix_trial"
}

// 添加 tag
func (t *Trial) Add(ctx context.Context, trialNew Trial) (Trial, error) {
	trialNew.Status = StatusNew
	trialNew.CreatedAt = time.Now()
	trialNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&trialNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return trialNew, err
}

// 获取信息
func (t *Trial) GetById(ctx context.Context, trialId def.AutoIDType) (Trial, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": trialId,
	}

	// 执行查询
	trialData := Trial{}
	err := db.Where(condWhere).First(&trialData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())
		return trialData, err
	}
	return trialData, nil
}

// 统计数据
func (t *Trial) Count(ctx context.Context, userId def.AutoIDType) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	if userId > 0 {
		db = db.Where("user_id = ?", userId)
	}

	// 执行查询
	var count def.CountType
	err := db.Model(t).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 获取数据
func (t *Trial) GetTrail(ctx context.Context, userID def.AutoIDType, taskType []def.EnumType,
	status []def.EnumType, limit, page def.IntType, orderBy ...string) ([]Trial, error) {
	// 获取 serial 列表
	db := resource.Gorm.WithContext(ctx)
	if userID > 0 {
		db = db.Where("user_id = ?", userID)
	}
	if len(status) > 0 {
		db = db.Where("status in ?", status)
	}
	if len(taskType) > 0 {
		db = db.Where("task_type in ?", taskType)
	}

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var trailList []Trial
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&trailList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return trailList, err
}

// 修改试用数据
func (t *Trial) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(t).Updates(&t).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (t *Trial) SwapData() map[string]any {
	var trialData map[string]any
	if t.ID <= 0 {
		return trialData
	}

	trialData = map[string]any{
		"id":         t.ID,
		"user_id":    t.UserId,
		"admin_id":   t.AdminId,
		"trial_type": t.Type,
		"task_type":  t.TaskType,
		"start_time": t.StartTime.Unix(),
		"end_time":   t.EndTime.Unix(),
		"used_num":   t.UsedNum,
		"limit_num":  t.LimitNum,
		"status":     t.Status,
		"created_at": t.CreatedAt.Unix(),
		"updated_at": t.UpdatedAt.Unix(),
	}
	return trialData
}
