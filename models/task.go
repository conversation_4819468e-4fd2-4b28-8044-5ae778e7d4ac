package models

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

// 任务运行状态
const (
	TaskStatusNew    int = 1  // 新任务
	TaskStatusDoing  int = 10 // 运行中
	TaskStatusCancel int = 20 // 取消
	TaskStatusSucc   int = 30 // 完成
	TaskStatusFailed int = 40 // 失败
)

const (
	TaskTypeAll int = 0 // 全部任务

	TaskTypeFold              def.EnumType = 10  // liner-fold
	TaskTypePartition         def.EnumType = 11  // liner-partition
	TaskTypeRNASerial         def.EnumType = 20  // 序列设计
	TaskType5UTR              def.EnumType = 25  // 5utr
	TaskTypeAdmet             def.EnumType = 30  // admet (支持训练)
	TaskTypeSelfAdmet         def.EnumType = 31  // admet (自训练)
	TaskTypeMolActivity       def.EnumType = 40  // 分子活性预测（序列）(支持训练)
	TaskTypeMolActStruct      def.EnumType = 41  // 分子活性预测（结构）
	TaskTypeMMGBSA            def.EnumType = 42  // MMGBSA
	TaskTypeHelixFoldAA       def.EnumType = 43  // HelixFoldAA
	TaskTypeHF3Agab           def.EnumType = 44  // HelixFold3Agab
	TaskTypeMiniProteinDesign def.EnumType = 45  // 小蛋白设计
	TaskTypeAntibodyDesign    def.EnumType = 46  // 抗体设计
	TaskTypeMolFormation      def.EnumType = 50  // 化合物设计（分子生成-基础生成）
	TaskTypeMolFormPath       def.EnumType = 53  // 化合物设计（分子生成-合成路径）
	TaskTypeHelixVSSyn        def.EnumType = 55  // 基于骨架（分子生成）
	TaskTypeProtein           def.EnumType = 60  // 蛋白质预测
	TaskTypeProteinSingle     def.EnumType = 61  // 蛋白质预测(序列)
	TaskTypeProteinRelaxation int          = 62  // 蛋白质Relaxation预测
	TaskTypeProteinComplex    def.EnumType = 65  // 蛋白复合物
	TaskTypeVirtualFilter     def.EnumType = 70  // 虚拟筛选（分子对接）
	TaskTypeHelixVS           def.EnumType = 71  // 虚拟筛选（）helixVS
	TaskTypeHelixDock         def.EnumType = 72  // HelixDock
	TaskTypeDoubleDrug        def.EnumType = 80  // 双药联合
	TaskTypeAntibody          def.EnumType = 90  // 抗原
	TaskTypeNewCrown          def.EnumType = 91  // 新冠
	TaskTypeProteinMutation   def.EnumType = 92  // 蛋白突变
	TaskTypeKYKT              def.EnumType = 93  // 抗原抗体
	TaskTypeProteinFunc       def.EnumType = 100 // 蛋白质功能
	TaskTypeExactDrug         def.EnumType = 105 // 精准用药
	TaskTypeCompound          def.EnumType = 110 // 分子逆合成
	// 靶点、配体
	BasedTypeTarget = "target"

	ChargeTabNo      int = 0
	ChargeTabCoupon  int = 10
	ChargeTabBilling int = 11
	ChargeTabFinish  int = 20
)

type TaskConfig struct {
	Serial        string `json:"serial"`
	SerialLen     int64  `json:"serial_len"`
	BeamSize      int64  `json:"beam_size"`
	ModelPath     string `json:"model_path"`
	PdbUrl        string `json:"pdb_url"`
	PdbName       string `json:"pdb_name"`
	FileUrl       string `json:"file_url"`
	Level         int64  `json:"level"`
	ProteinUrl    string `json:"protein_url"`
	MolName       string `json:"mol_name"`
	MolUrl        string `json:"mol_url"`
	GbModel       int64  `json:"gb_model"`
	SimulationLen int64  `json:"simulation_len"`
	Epsilon       int64  `json:"epsilon"`
}

type PushBillingTaskConfig struct {
	Level  int   `json:"level"`
	Number int64 `json:"number"`
}

var TaskTypeList = map[def.EnumType]bool{
	TaskTypeFold:              true,
	TaskTypePartition:         true,
	TaskTypeRNASerial:         true,
	TaskType5UTR:              true,
	TaskTypeAdmet:             true,
	TaskTypeSelfAdmet:         true,
	TaskTypeMolActivity:       true,
	TaskTypeMolActStruct:      true,
	TaskTypeMolFormation:      true,
	TaskTypeProtein:           true,
	TaskTypeProteinSingle:     true,
	TaskTypeProteinComplex:    true,
	TaskTypeVirtualFilter:     true,
	TaskTypeHelixVS:           true,
	TaskTypeDoubleDrug:        true,
	TaskTypeAntibody:          true,
	TaskTypeNewCrown:          true,
	TaskTypeProteinMutation:   true,
	TaskTypeProteinFunc:       true,
	TaskTypeExactDrug:         true,
	TaskTypeCompound:          true,
	TaskTypeMMGBSA:            true,
	TaskTypeMolFormPath:       true,
	TaskTypeHelixVSSyn:        true,
	TaskTypeHelixDock:         true,
	TaskTypeKYKT:              true,
	TaskTypeHF3Agab:           true,
	TaskTypeMiniProteinDesign: true,
	TaskTypeAntibodyDesign:    true,
}

// 表结构
type Task struct {
	ID              def.AutoIDType `gorm:"id"`
	UserId          def.AutoIDType `gorm:"user_id"`
	ProjectId       def.AutoIDType `gorm:"project_id"`
	Type            def.EnumType   `gorm:"type"`
	FuncType        def.EnumType   `gorm:"func_type"`
	Name            string         `gorm:"name"`
	Config          string         `gorm:"config"`
	Result          string         `gorm:"result"`
	ServerTaskId    def.AutoIDType `gorm:"server_task_id"`
	IsExample       def.EnumType   `gorm:"is_example"`
	Status          def.EnumType   `gorm:"status"`
	UseTime         time.Time      `gorm:"use_time"`
	ChargeStartTime time.Time      `gorm:"charge_start_time"`
	ChargeEndTime   time.Time      `gorm:"charge_end_time"`
	FinishTime      time.Time      `gorm:"finish_time"`
	Resource        string         `gorm:"resource"`
	ParentId        def.AutoIDType `gorm:"parent_id"`
	HadRead         def.EnumType   `gorm:"had_read"`
	ChargeTab       def.EnumType   `gorm:"charge_tab"`
	CreatedAt       time.Time      `gorm:"created_at"`
	UpdatedAt       time.Time      `gorm:"updated_at"`
	IsAPI           int32          `gorm:"is_api"`
	JobFailReason   string         `gorm:"job_fail_reason"`
	NTokens         uint64         `gorm:"n_tokens"`
	Coupons         string         `gorm:"coupons"`
	Balance         float64        `gorm:"balance"`
	OrderID         string         `gorm:"order_id"`
}

// 表名
func (t *Task) TableName() string {
	return "helix_task"
}

// task search
type TaskCond struct {
	UserIdList       []def.AutoIDType
	FilterUserIdList []string
	TypeList         []def.EnumType
	ProjectId        def.AutoIDType
	BasedType        string
	IsAPI            int32
	Keyword          string
	GtCreatedAt      string
	LtCreatedAt      string
	Status           int32
	Type             int32
}

// 通过Id获取用户信息
func (t *Task) GetByIds(ctx context.Context, taskIdList []def.AutoIDType) ([]Task, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": taskIdList,
	}

	// 执行查询
	var list []Task
	err := db.Where(condWhere).Order("id asc").Find(&list).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return list, err
}

// 获取任务信息
func (t *Task) GetTaskById(ctx context.Context, taskId def.AutoIDType) (Task, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": taskId,
	}

	// 执行查询
	taskData := Task{}
	err := db.Where(condWhere).First(&taskData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())
		return taskData, err
	}
	return taskData, nil
}

// 查询列表
func (t *Task) GetByCond(ctx context.Context, cond TaskCond, limit, page def.IntType, orderBy ...string) ([]Task, error) {
	db := resource.Gorm.WithContext(ctx)
	db = taskBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var taskDataList []Task
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&taskDataList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return taskDataList, err
}

// 统计数据
func (t *Task) CountByCond(ctx context.Context, cond TaskCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = taskBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(t).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 统计数据
func (t *Task) CountDistinctByCond(ctx context.Context, cond TaskCond, distinct string) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = taskBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(t).Distinct(distinct).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 数据转化
func (t *Task) SwapData() map[string]any {
	taskData := make(map[string]any)
	if t.ID <= 0 {
		return taskData
	}

	// 处理数据
	var config map[string]any
	_ = json.Unmarshal([]byte(t.Config), &config)

	var result map[string]any
	_ = json.Unmarshal([]byte(t.Result), &result)

	taskData = map[string]any{
		"task_id":           t.ID,
		"user_id":           t.UserId,
		"name":              t.Name,
		"type":              t.Type,
		"func_type":         t.FuncType,
		"status":            t.Status,
		"config":            config,
		"result":            result,
		"is_example":        t.IsExample,
		"use_time":          SwapFieldUnix(t.UseTime.Unix()),
		"charge_start_time": SwapFieldUnix(t.ChargeStartTime.Unix()),
		"charge_end_time":   SwapFieldUnix(t.ChargeEndTime.Unix()),
		"finish_time":       SwapFieldUnix(t.FinishTime.Unix()),
		"resource":          t.Resource,
		"parent_id":         t.ParentId,
		"had_read":          t.HadRead,
		"created_at":        t.CreatedAt.Unix(),
		"updated_at":        t.UpdatedAt.Unix(),
		"is_api":            t.IsAPI,
		"job_fail_reason":   t.JobFailReason,
	}
	return taskData
}

// where build
func taskBuildWhere(db *gorm.DB, cond TaskCond) *gorm.DB {
	//db = db.Where("status <> ?", StatusDel)

	if len(cond.TypeList) > 0 {
		db = db.Where("type in (?)", cond.TypeList)
	}
	if len(cond.UserIdList) > 0 {
		db = db.Where("(user_id in (?))", cond.UserIdList)
	}
	if cond.ProjectId > 0 {
		db = db.Where("(project_id = ?)", cond.ProjectId)
	}
	if cond.IsAPI >= 0 {
		db = db.Where("(is_api = ?)", cond.IsAPI)
	}
	if len(cond.FilterUserIdList) > 0 {
		db = db.Where("(user_id not in (?))", cond.FilterUserIdList)
	}
	if len(cond.Keyword) > 0 {
		db = db.Where("(id = ? OR name like ?)", cond.Keyword, "%"+cond.Keyword+"%")
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}
	if cond.Status > 0 {
		db = db.Where("status = ?", cond.Status)
	}
	if cond.Type > 0 {
		db = db.Where("type = ?", cond.Type)
	}
	return db
}

func (t *Task) Add(ctx context.Context, taskNew Task, status ...int) (Task, error) {
	taskNew.Status = def.EnumType(1)
	taskNew.UseTime = time.Now()
	taskNew.CreatedAt = time.Now()
	taskNew.UpdatedAt = time.Now()

	if len(status) > 0 {
		taskNew.Status = def.EnumType(status[0])
	}
	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&taskNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return taskNew, err
}

func (t *Task) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(t).Updates(&t).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return err
}
