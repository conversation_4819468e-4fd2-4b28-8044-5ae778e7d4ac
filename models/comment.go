package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

type Comment struct {
	ID           def.AutoIDType `gorm:"id"`
	UserId       def.AutoIDType `gorm:"user_id"`
	Type         def.EnumType   `gorm:"type"`
	Username     string         `gorm:"username"`
	Score        float64        `gorm:"score"`
	Content      string         `gorm:"content"`
	OpenContent  string         `gorm:"open_content"`
	ExtraComment string         `gorm:"extra_comment"`
	AwardNum     def.CountType  `gorm:"award_num"`
	TaskId       def.AutoIDType `gorm:"task_id"`
	TaskType     def.EnumType   `gorm:"task_type"`
	CreatedAt    time.Time      `gorm:"created_at"`
	UpdatedAt    time.Time      `gorm:"updated_at"`
}

func (c *Comment) TableName() string {
	return "helix_user_comment"
}

// 获取 Comment 列表
func (c *Comment) GetList(ctx context.Context, cond CommentCond, limit, page def.IntType, orderBy ...string) ([]Comment, error) {
	db := resource.Gorm.WithContext(ctx)
	db = CommentBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var dataList []Comment
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&dataList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return dataList, err
}

// 统计数据
func (c *Comment) CountByCond(ctx context.Context, cond CommentCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = CommentBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(c).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (c *Comment) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(c).Updates(&c).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (c *Comment) SwapData() map[string]any {
	var data map[string]any
	if c.ID <= 0 {
		return data
	}

	var content []string
	_ = json.Unmarshal([]byte(c.Content), &content)

	var extraComment []map[string]any
	_ = json.Unmarshal([]byte(c.ExtraComment), &extraComment)

	data = map[string]any{
		"id":            c.ID,
		"user_id":       c.UserId,
		"username":      c.Username,
		"type":          c.Type,
		"score":         c.Score,
		"content":       content,
		"open_content":  c.OpenContent,
		"extra_comment": extraComment,
		"award_num":     c.AwardNum,
		"task_id":       c.TaskId,
		"task_type":     c.TaskType,
		"created_at":    c.CreatedAt.Unix(),
		"updated_at":    c.UpdatedAt.Unix(),
	}
	return data
}

// user search
type CommentCond struct {
	Keyword          string
	GtCreatedAt      string
	LtCreatedAt      string
	TypeList         []def.EnumType
	MinScore         float64
	MaxScore         float64
	TaskTypeList     []def.EnumType
	FilterUserIdList []string
}

// where build
func CommentBuildWhere(db *gorm.DB, cond CommentCond) *gorm.DB {
	if len(cond.Keyword) > 0 {
		db = db.Where("(user_id like ? OR username like ?)", "%"+cond.Keyword+"%", "%"+cond.Keyword+"%")
	}
	if len(cond.FilterUserIdList) > 0 {
		db = db.Where("(user_id not in (?))", cond.FilterUserIdList)
	}
	if len(cond.TypeList) > 0 {
		db = db.Where("type in (?)", cond.TypeList)
	}
	if cond.MinScore > 0 {
		db = db.Where("score >= ?", cond.MinScore)
	}
	if cond.MaxScore > 0 {
		db = db.Where("score <= ?", cond.MaxScore)
	}
	if len(cond.TaskTypeList) > 0 {
		db = db.Where("task_type in (?)", cond.TaskTypeList)
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at <= ?", cond.LtCreatedAt)
	}
	return db
}
