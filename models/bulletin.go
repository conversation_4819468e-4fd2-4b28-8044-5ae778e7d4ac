package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

type Bulletin struct {
	ID        def.AutoIDType `gorm:"id"`
	AdminId   def.AutoIDType `gorm:"admin_id"`
	Title     string         `gorm:"title"`
	Content   string         `gorm:"content"`
	ContentEN string         `gorm:"content_en"`
	Url       string         `gorm:"url"`
	Sort      def.EnumType   `gorm:"sort"`
	Status    def.EnumType   `gorm:"status"`
	ShowTime  time.Time      `gorm:"show_time"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (b *Bulletin) TableName() string {
	return "helix_bulletin"
}

func (b *Bulletin) GetList(ctx context.Context, limit, page def.IntType, orderBy ...string) ([]Bulletin, error) {
	db := resource.Gorm.WithContext(ctx)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var list []Bulletin
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&list).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return list, err
}

// 统计数据
func (b *Bulletin) CountByCond(ctx context.Context) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)

	// 执行查询
	var count def.CountType
	err := db.Model(b).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 通过Id获取信息
func (b *Bulletin) GetById(ctx context.Context, id def.AutoIDType) (Bulletin, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	data := Bulletin{}
	err := db.Where(condWhere).First(&data).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return data, err
	}
	return data, nil
}

func (b *Bulletin) Add(ctx context.Context, bulletinNew Bulletin) (Bulletin, error) {
	bulletinNew.Status = StatusNew
	bulletinNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&bulletinNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return bulletinNew, err
}

// 保存、修改
func (b *Bulletin) Save(ctx context.Context) error {
	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(b).Updates(&b).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (b *Bulletin) SwapData() map[string]interface{} {
	var data map[string]interface{}
	if b.ID <= 0 {
		return data
	}

	data = map[string]interface{}{
		"id":         b.ID,
		"admin_id":   b.AdminId,
		"title":      b.Title,
		"content":    b.Content,
		"content_en": b.ContentEN,
		"status":     b.Status,
		"url":        b.Url,
		"show_time":  SwapFieldUnix(b.ShowTime.Unix()),
		"created_at": b.CreatedAt.Unix(),
		"updated_at": b.UpdatedAt.Unix(),
	}
	return data
}
