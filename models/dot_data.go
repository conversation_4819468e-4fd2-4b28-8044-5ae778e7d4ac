package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	DotTypeNor  def.EnumType = 10
	DotTypeHome def.EnumType = 11
	DotTypePV   def.EnumType = 20
	DotTypeHQDL def.EnumType = 30
	DotTypeWCDL def.EnumType = 40
)

type DotData struct {
	ID        def.AutoIDType `gorm:"id"`
	UserId    def.AutoIDType `gorm:"user_id"`
	Type      def.EnumType   `gorm:"type"`
	Host      string         `gorm:"host"`
	Content   string         `gorm:"content"`
	CreatedAt time.Time      `gorm:"created_at"`
}

func (d *DotData) TableName() string {
	return "helix_dot_data"
}

type DotCond struct {
	MinTime  string
	MaxTime  string
	TypeList []def.EnumType
}
type DotSumRes struct {
	Type def.EnumType
	Date time.Time
	Num  def.CountType
	UV   def.CountType
}

func (d *DotData) Sum(ctx context.Context, cond DotCond) ([]DotSumRes, error) {
	db := resource.Gorm.WithContext(ctx)
	db = dotBuildWhere(db, cond)

	// 执行查询
	var res []DotSumRes
	err := db.Model(d).Select("type, count(id) as num, count(distinct(host)) as uv").Group("type").Find(&res).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return res, err
}

func (d *DotData) SumByCond(ctx context.Context, cond DotCond) ([]DotSumRes, error) {
	db := resource.Gorm.WithContext(ctx)
	db = dotBuildWhere(db, cond)

	// 执行查询
	var res []DotSumRes
	err := db.Model(d).Select("Date(created_at) as date, type, count(id) as num, count(distinct(host)) as uv").Group("date, type").Find(&res).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return res, err
}

func (d *DotData) GetList(ctx context.Context, cond DotCond, limit, page def.IntType, orderBy ...string) ([]DotData, error) {
	db := resource.Gorm.WithContext(ctx)
	db = dotBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var list []DotData
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&list).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return list, err
}

// where build
func dotBuildWhere(db *gorm.DB, cond DotCond) *gorm.DB {
	if len(cond.TypeList) > 0 {
		db = db.Where("type in (?)", cond.TypeList)
	}
	if len(cond.MinTime) > 0 {
		db = db.Where("created_at >= ?", cond.MinTime)
	}
	if len(cond.MaxTime) > 0 {
		db = db.Where("created_at < ?", cond.MaxTime)
	}
	return db
}

func (d *DotData) SwapData() map[string]any {
	var data map[string]any
	if d.ID <= 0 {
		return data
	}

	var content map[string]any
	_ = json.Unmarshal([]byte(d.Content), &content)

	data = map[string]any{
		"id":         d.ID,
		"type":       d.Type,
		"content":    content,
		"created_at": d.CreatedAt.Unix(),
	}
	return data
}
