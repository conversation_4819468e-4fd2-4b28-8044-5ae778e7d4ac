package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	AdminStatusNor def.EnumType = 10
	AdminStatusDel def.EnumType = -1
)

// Apply 表结构
type Admin struct {
	ID            def.AutoIDType `gorm:"id"`
	Username      string         `gorm:"username"`
	Password      string         `gorm:"password"`
	HeadUrl       string         `gorm:"head_url"`
	RoleId        def.AutoIDType `gorm:"role_id"`
	PermissionIds string         `gorm:"permission_ids"`
	Status        def.EnumType   `gorm:"status"`
	LoginTime     time.Time      `gorm:"login_time"`
	CreatedAt     time.Time      `gorm:"created_at"`
	UpdatedAt     time.Time      `gorm:"updated_at"`
}

// UserInfo 结构体
type AdminInfo struct {
	AdminId           def.AutoIDType   `json:"admin_id"`
	Username          string           `json:"username"`
	<PERSON><PERSON><PERSON><PERSON>         string           `json:"role_alias"`
	RoleId            def.AutoIDType   `json:"role_id"`
	PermissionIds     []def.AutoIDType `json:"permission_ids"`
	PermissionUriList []string         `json:"permission_uri_list"`
}

func (a *Admin) TableName() string {
	return "manage_admin"
}

// 添加管理员
func (a *Admin) Add(ctx context.Context, adminNew Admin) (Admin, error) {
	adminNew.Status = AdminStatusNor
	adminNew.CreatedAt = time.Now()
	adminNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&adminNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return adminNew, err
}

// 通过Id获取用户信息
func (a *Admin) GetById(ctx context.Context, adminId def.AutoIDType) (Admin, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": adminId,
	}

	// 执行查询
	adminData := Admin{}
	err := db.Where(condWhere).First(&adminData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())
		return adminData, err
	}
	return adminData, nil
}

// 通过Id获取用户信息
func (a *Admin) GetByIds(ctx context.Context, adminIdList []def.AutoIDType) ([]Admin, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": adminIdList,
	}

	// 执行查询
	var adminList []Admin
	err := db.Where(condWhere).Order("id asc").Find(&adminList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return adminList, err
}

// 获取所有数据
func (a *Admin) GetList(ctx context.Context, roleIdList []def.AutoIDType) ([]Admin, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"status": AdminStatusNor,
	}
	if len(roleIdList) > 0 {
		condWhere["role_id"] = roleIdList
	}

	// 执行查询
	var adminList []Admin
	err := db.Where(condWhere).Order("id asc").Find(&adminList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return adminList, err
}

// 通过用户名获取用户信息
func (a *Admin) GetByUsername(ctx context.Context, username string) (Admin, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"username": username,
		"status":   AdminStatusNor,
	}

	// 执行查询
	adminData := Admin{}
	err := db.Where(condWhere).First(&adminData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())
		return adminData, err
	}
	return adminData, nil
}

// 修改用户数据
func (a *Admin) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(a).Updates(&a).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return err
}

// 数据转化
func (a *Admin) SwapData() map[string]any {
	var userData map[string]any
	if a.ID <= 0 {
		return userData
	}

	var permissionIdList []def.AutoIDType
	_ = json.Unmarshal([]byte(a.PermissionIds), &permissionIdList)

	userData = map[string]any{
		"admin_id":       a.ID,
		"username":       a.Username,
		"head_url":       a.HeadUrl,
		"role_id":        a.RoleId,
		"permission_ids": permissionIdList,
		"login_time":     SwapFieldUnix(a.LoginTime.Unix()),
		"created_at":     a.CreatedAt.Unix(),
		"updated_at":     a.UpdatedAt.Unix(),
	}
	return userData
}

// 校验密码
func (a *Admin) CheckPassword(password string, encryptPassword string) bool {
	encryptPass := a.EncryptPassword(password)
	if encryptPass != encryptPassword {
		return false
	}

	return true
}

// 密码加密
func (a *Admin) EncryptPassword(password string) string {
	return helpers.Md5(helpers.Md5(password + "helix"))
}
