package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	FuncTypeTrain              def.EnumType = 10
	FuncTypeTrainClassify      def.EnumType = 11
	FuncTypeForecast           def.EnumType = 20
	FuncTypeForecastRNA        def.EnumType = 21
	FuncTypeForecastCodon      def.EnumType = 22
	SerialFuncTypeForecast3UTR def.EnumType = 23
	SerialFuncTypeForecast5UTR def.EnumType = 24
	FuncTypePretreat           def.EnumType = 30
	FuncTypeNoStruct           def.EnumType = 40

	DataTypeString   def.EnumType = 10
	DataTypeFile     def.EnumType = 20
	DataTypeFileMore def.EnumType = 21
)

type Serial struct {
	ID        def.AutoIDType `gorm:"id"`
	AdminId   def.AutoIDType `gorm:"admin_id"`
	Type      def.EnumType   `gorm:"type"`
	FuncType  def.EnumType   `gorm:"func_type"`
	DataType  def.EnumType   `gorm:"data_type"`
	Name      string         `gorm:"name"`
	Desc      string         `gorm:"desc"`
	Protein   string         `gorm:"protein"`
	Serial    string         `gorm:"serial"`
	Config    string         `gorm:"config"`
	FileUrl   string         `gorm:"file_url"`
	Status    def.EnumType   `gorm:"status"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (s *Serial) TableName() string {
	return "helix_serial"
}

// serial search
type SerialCond struct {
	AdminId  def.AutoIDType
	Type     def.EnumType
	FuncType def.EnumType
	DataType def.EnumType
	Status   def.EnumType
	Name     string
}

// 通过Id获取信息
func (s *Serial) GetById(ctx context.Context, id def.AutoIDType) (Serial, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	serialData := Serial{}
	err := db.Where(condWhere).First(&serialData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return serialData, err
	}
	return serialData, nil
}

// 添加 serial
func (s *Serial) Add(ctx context.Context, serialNew Serial) (Serial, error) {
	serialNew.Status = StatusNew
	serialNew.CreatedAt = time.Now()
	serialNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&serialNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return serialNew, err
}

// 获取 serial 列表
func (s *Serial) GetList(ctx context.Context, cond SerialCond, limit, page def.IntType, orderBy ...string) ([]Serial, error) {
	db := resource.Gorm.WithContext(ctx)
	db = serialBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var serialList []Serial
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&serialList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return serialList, err
}

// 统计数据
func (s *Serial) CountByCond(ctx context.Context, cond SerialCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = serialBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(s).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (s *Serial) Save(ctx context.Context) error {
	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(s).Updates(&s).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (s *Serial) SwapData() map[string]any {
	var data map[string]any
	if s.ID <= 0 {
		return data
	}

	var conf map[string]any
	_ = json.Unmarshal([]byte(s.Config), &conf)

	data = map[string]any{
		"id":         s.ID,
		"admin_id":   s.AdminId,
		"type":       s.Type,
		"func_type":  s.FuncType,
		"data_type":  s.DataType,
		"name":       s.Name,
		"protein":    s.Protein,
		"serial":     s.Serial,
		"config":     conf,
		"file_url":   s.FileUrl,
		"status":     s.Status,
		"desc":       s.Desc,
		"created_at": s.CreatedAt.Unix(),
		"updated_at": s.UpdatedAt.Unix(),
	}
	return data
}

// where build
func serialBuildWhere(db *gorm.DB, cond SerialCond) *gorm.DB {
	if cond.AdminId > 0 {
		db = db.Where("admin_id = ?", cond.AdminId)
	}
	if cond.Type > 0 {
		db = db.Where("type = ?", cond.Type)
	}
	if cond.FuncType > 0 {
		db = db.Where("func_type = ?", cond.FuncType)
	}
	if cond.DataType > 0 {
		db = db.Where("data_type = ?", cond.DataType)
	}
	if len(cond.Name) > 0 {
		db = db.Where("name = ?", cond.Name)
	}
	if cond.Status > 0 {
		db = db.Where("status = ?", cond.Status)
	} else {
		db = db.Where("status <> ?", StatusDel)
	}

	return db
}
