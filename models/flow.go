package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

// 计费流量总览
type Flow struct {
	ID         def.AutoIDType    `gorm:"id"`
	UserId     def.AutoIDType    `gorm:"user_id"`
	TaskId     def.AutoIDType    `gorm:"task_id"`
	TaskType   def.EnumType      `gorm:"task_type"`
	ChargeTime def.TimestampType `gorm:"charge_time"`
	CreatedAt  time.Time         `gorm:"created_at"`
	UpdatedAt  time.Time         `gorm:"updated_at"`
}

func (f *Flow) TableName() string {
	return "helix_flow"
}

// flow search
type FlowCond struct {
	UserIdList  []def.AutoIDType
	LtCreatedAt string
	GtCreatedAt string
}

type FlowSumRes struct {
	UserId def.AutoIDType
	Total  def.CountType
	Num    def.CountType
}

func (f *Flow) GroupSum(ctx context.Context, cond FlowCond) ([]FlowSumRes, error) {
	db := resource.Gorm.WithContext(ctx)
	db = flowBuildWhere(db, cond)

	// 执行查询
	var groupResultList []FlowSumRes
	selectStr := "user_id, count(*) as total, sum(charge_time) as num"
	err := db.Model(f).Select(selectStr).Group("user_id").Find(&groupResultList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return groupResultList, err
}

// where build
func flowBuildWhere(db *gorm.DB, cond FlowCond) *gorm.DB {
	if len(cond.UserIdList) > 0 {
		db = db.Where("user_id in (?)", cond.UserIdList)
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	return db
}
