package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"icode.baidu.com/helix_web/library/def"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

type Portrait struct {
	ID        def.AutoIDType `gorm:"id"`
	UserId    def.AutoIDType `gorm:"user_id"`
	Username  string         `gorm:"username"`
	Channel   string         `gorm:"channel"`
	Demand    string         `gorm:"demand"`
	Industry  string         `gorm:"industry"`
	Career    string         `gorm:"career"`
	AwardNum  def.CountType  `gorm:"award_num"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (p *Portrait) TableName() string {
	return "helix_portrait"
}

// GetList 获取 portrait 列表
func (p *Portrait) GetList(ctx context.Context, cond PortraitCond, limit, page def.IntType, orderBy ...string) ([]Portrait, error) {
	db := resource.Gorm.WithContext(ctx)
	db = portraitBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var dataList []Portrait
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&dataList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return dataList, err
}

// 统计数据
func (p *Portrait) CountByCond(ctx context.Context, cond PortraitCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = portraitBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(p).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (p *Portrait) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(p).Updates(&p).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (p *Portrait) SwapData() map[string]any {
	var data map[string]any
	if p.ID <= 0 {
		return data
	}

	var demand []string
	_ = json.Unmarshal([]byte(p.Demand), &demand)

	data = map[string]any{
		"id":          p.ID,
		"user_id":     p.UserId,
		"username":    p.Username,
		"channel":     p.Channel,
		"demand_list": demand,
		"industry":    p.Industry,
		"career":      p.Career,
		"award_num":   p.AwardNum,
		"created_at":  p.CreatedAt.Unix(),
		"updated_at":  p.UpdatedAt.Unix(),
	}
	return data
}

// user search
type PortraitCond struct {
	Keyword          string
	GtCreatedAt      string
	LtCreatedAt      string
	FilterUserIdList []string
}

// where build
func portraitBuildWhere(db *gorm.DB, cond PortraitCond) *gorm.DB {
	if len(cond.Keyword) > 0 {
		db = db.Where("(user_id like ? OR username like ?)", "%"+cond.Keyword+"%", "%"+cond.Keyword+"%")
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at <= ?", cond.LtCreatedAt)
	}
	if len(cond.FilterUserIdList) > 0 {
		db = db.Where("(user_id not in (?))", cond.FilterUserIdList)
	}
	return db
}
