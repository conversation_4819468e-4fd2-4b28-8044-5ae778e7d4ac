package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	FeedbackStatusNew  def.EnumType = 10
	FeedbackStatusDone def.EnumType = 20
	FeedbackStatusDel  def.EnumType = 1
)

type Feedback struct {
	ID        def.AutoIDType `gorm:"id"`
	AdminId   def.AutoIDType `gorm:"admin_id"`
	Name      string         `gorm:"name"`
	Phone     string         `gorm:"phone"`
	Email     string         `gorm:"email"`
	Company   string         `gorm:"company"`
	Content   string         `gorm:"content"`
	Status    def.EnumType   `gorm:"status"`
	Remark    string         `gorm:"remark"`
	DealTime  time.Time      `gorm:"deal_time"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (f *Feedback) TableName() string {
	return "helix_feedback"
}

// 通过Id获取信息
func (f *Feedback) GetById(ctx context.Context, id def.AutoIDType) (Feedback, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	feedbackData := Feedback{}
	err := db.Where(condWhere).First(&feedbackData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return feedbackData, err
	}
	return feedbackData, nil
}

// 获取 Feedback 列表
func (f *Feedback) GetList(ctx context.Context, cond FeedbackCond, limit, page def.IntType, orderBy ...string) ([]Feedback, error) {
	db := resource.Gorm.WithContext(ctx)
	db = feedbackBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var feedbackList []Feedback
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&feedbackList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return feedbackList, err
}

// 统计数据
func (f *Feedback) CountByCond(ctx context.Context, cond FeedbackCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = feedbackBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(f).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (f *Feedback) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(f).Updates(&f).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (f *Feedback) SwapData() map[string]any {
	var feedbackData map[string]any
	if f.ID <= 0 {
		return feedbackData
	}

	feedbackData = map[string]any{
		"id":         f.ID,
		"admin_id":   f.AdminId,
		"name":       f.Name,
		"phone":      f.Phone,
		"email":      f.Email,
		"company":    f.Company,
		"content":    f.Content,
		"status":     f.Status,
		"remark":     f.Remark,
		"deal_name":  SwapFieldUnix(f.DealTime.Unix()),
		"created_at": f.CreatedAt.Unix(),
		"updated_at": f.UpdatedAt.Unix(),
	}
	return feedbackData
}

// user search
type FeedbackCond struct {
	Status      def.EnumType
	Keyword     string
	GtCreatedAt string
	LtCreatedAt string
}

// where build
func feedbackBuildWhere(db *gorm.DB, cond FeedbackCond) *gorm.DB {
	if len(cond.Keyword) > 0 {
		db = db.Where("(name like ? OR remark like ?)", "%"+cond.Keyword+"%", "%"+cond.Keyword+"%")
	}
	if cond.Status > 0 {
		db = db.Where("status = ?", cond.Status)
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}
	return db
}
