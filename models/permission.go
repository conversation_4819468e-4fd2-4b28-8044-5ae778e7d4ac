package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

// 表结构
type Permission struct {
	ID        def.AutoIDType `gorm:"id"`
	Name      string         `gorm:"name"`
	Uri       string         `gorm:"uri"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (p *Permission) TableName() string {
	return "manage_permission"
}

// 添加 permission
func (p *Permission) Add(ctx context.Context, permissionNew Permission) (Permission, error) {
	permissionNew.CreatedAt = time.Now()
	permissionNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&permissionNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return permissionNew, err
}

// 通过uri获取
func (p *Permission) GetByUri(ctx context.Context, uri string) (Permission, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"uri": uri,
	}

	// 执行查询
	permissionData := Permission{}
	err := db.Where(condWhere).First(&permissionData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return permissionData, err
	}
	return permissionData, nil
}

// 通过ids获取
func (p *Permission) GetByIds(ctx context.Context, ids []def.AutoIDType) ([]Permission, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": ids,
	}

	// 执行查询
	var permissionList []Permission
	err := db.Where(condWhere).Find(&permissionList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return permissionList, err
}

// 获取所有数据
func (p *Permission) GetList(ctx context.Context) ([]Permission, error) {
	db := resource.Gorm.WithContext(ctx)

	// 执行查询
	var permissionList []Permission
	err := db.Order("id asc").Find(&permissionList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return permissionList, err
}

// 数据转化
func (p *Permission) SwapData() map[string]any {
	var data map[string]any
	if p.ID <= 0 {
		return data
	}

	data = map[string]any{
		"id":         p.ID,
		"name":       p.Name,
		"uri":        p.Uri,
		"created_at": p.CreatedAt.Unix(),
		"updated_at": p.UpdatedAt.Unix(),
	}
	return data
}
