package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	ModuleTypeScene def.EnumType = 10
	ModuleTypePower def.EnumType = 20
)

var ModuleTypeMap = map[def.EnumType]bool{
	ModuleTypeScene: true,
	ModuleTypePower: true,
}

type HomeModule struct {
	ID        def.AutoIDType `gorm:"id"`
	AdminId   def.AutoIDType `gorm:"admin_id"`
	Type      def.EnumType   `gorm:"type"`
	Status    def.EnumType   `gorm:"status"`
	Data      string         `gorm:"data"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (h *HomeModule) TableName() string {
	return "helix_home_module"
}

// GetAll 获取信息
func (h *HomeModule) GetAll(ctx context.Context) ([]HomeModule, error) {
	db := resource.Gorm.WithContext(ctx)
	condWhere := map[string]any{
		"status": StatusNew,
	}

	// 执行查询
	var list []HomeModule
	err := db.Where(condWhere).Find(&list).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return list, err
	}
	return list, nil
}

func (h *HomeModule) GetByType(ctx context.Context, tType def.EnumType) (HomeModule, error) {
	db := resource.Gorm.WithContext(ctx)
	condWhere := map[string]any{
		"type":   tType,
		"status": StatusNew,
	}

	// 执行查询
	HomeModuleData := HomeModule{}
	err := db.Where(condWhere).First(&HomeModuleData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return HomeModuleData, err
	}
	return HomeModuleData, nil
}

// 添加 HomeModule
func (h *HomeModule) Add(ctx context.Context, HomeModuleNew HomeModule) (HomeModule, error) {
	HomeModuleNew.Status = StatusNew
	HomeModuleNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&HomeModuleNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return HomeModuleNew, err
}

// 保存、修改
func (h *HomeModule) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(h).Updates(&h).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return err
}

// SwapData 数据转化
func (h *HomeModule) SwapData() map[string]any {
	var data map[string]any
	if h.ID <= 0 {
		return data
	}

	var content any
	_ = json.Unmarshal([]byte(h.Data), &content)

	data = map[string]any{
		"id":         h.ID,
		"admin_id":   h.AdminId,
		"type":       h.Type,
		"status":     h.Status,
		"data":       content,
		"created_at": h.CreatedAt.Unix(),
		"updated_at": h.UpdatedAt.Unix(),
	}
	return data
}
