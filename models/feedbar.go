package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	FeedbarTypeAchievement def.EnumType = 10
	FeedbarTypeAward       def.EnumType = 20
	FeedbarTypeBusiness    def.EnumType = 30
	FeedbarTypeVersion     def.EnumType = 40

	FeedTypeEducation     def.EnumType = 50
	FeedTypeCase          def.EnumType = 51
	FeedTypeTechnical     def.EnumType = 52
	FeedTypeCollaboration def.EnumType = 53
	FeedTypeAward         def.EnumType = 54
)

var FeedbarTypeList = map[def.EnumType]bool{
	FeedbarTypeAchievement: true,
	FeedbarTypeAward:       true,
	FeedbarTypeBusiness:    true,
	FeedbarTypeVersion:     true,
	FeedTypeEducation:      true,
	FeedTypeCase:           true,
	FeedTypeTechnical:      true,
	FeedTypeCollaboration:  true,
	FeedTypeAward:          true,
}

type Feedbar struct {
	ID          def.AutoIDType `gorm:"id"`
	AdminId     def.AutoIDType `gorm:"admin_id"`
	Content     string         `gorm:"content"`
	ContentEN   string         `gorm:"content_en"`
	Url         string         `gorm:"url"`
	Sort        def.CountType  `gorm:"sort"`
	Type        def.EnumType   `gorm:"type"`
	Status      def.EnumType   `gorm:"status"`
	PicUrl      string         `gorm:"pic_url"`
	IsHome      def.EnumType   `gorm:"is_home"`
	IsTop       def.EnumType   `gorm:"is_top"`
	DisplayTime time.Time      `gorm:"display_time"`
	CreatedAt   time.Time      `gorm:"created_at"`
	UpdatedAt   time.Time      `gorm:"updated_at"`
}

func (f *Feedbar) TableName() string {
	return "helix_feed_bar"
}

// 通过Id获取信息
func (f *Feedbar) GetById(ctx context.Context, id def.AutoIDType) (Feedbar, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	feedbarData := Feedbar{}
	err := db.Where(condWhere).First(&feedbarData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return feedbarData, err
	}
	return feedbarData, nil
}

// 添加 feedbar
func (f *Feedbar) Add(ctx context.Context, feedbarNew Feedbar) (Feedbar, error) {
	feedbarNew.Status = StatusNew
	feedbarNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&feedbarNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return feedbarNew, err
}

// 获取 feedbar 列表
func (f *Feedbar) GetList(ctx context.Context, limit, page def.IntType, orderBy ...string) ([]Feedbar, error) {
	db := resource.Gorm.WithContext(ctx)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var feedbarList []Feedbar
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&feedbarList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return feedbarList, err
}

// 统计数据
func (f *Feedbar) CountByCond(ctx context.Context) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)

	// 执行查询
	var count def.CountType
	err := db.Model(f).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (f *Feedbar) Save(ctx context.Context) error {
	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(f).Updates(&f).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

func (f *Feedbar) Update(ctx context.Context) error {
	dataMap := map[string]any{
		"id":         f.ID,
		"is_home":    f.IsHome,
		"is_top":     f.IsTop,
		"status":     f.Status,
		"updated_at": time.Now(),
	}

	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(f).Updates(&dataMap).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

func (f *Feedbar) BatchUpdateByTop(ctx context.Context, isTop def.EnumType) error {
	dataMap := map[string]any{
		"is_top": isTop,
	}
	condWhere := map[string]any{
		"is_top": 1,
	}

	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(f).Where(condWhere).Updates(&dataMap).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// SwapData 数据转化
func (f *Feedbar) SwapData() map[string]any {
	var data map[string]any
	if f.ID <= 0 {
		return data
	}

	data = map[string]any{
		"id":           f.ID,
		"admin_id":     f.AdminId,
		"url":          f.Url,
		"type":         f.Type,
		"content":      f.Content,
		"content_en":   f.ContentEN,
		"status":       f.Status,
		"sort":         f.Sort,
		"pic_url":      f.PicUrl,
		"is_home":      f.IsHome,
		"is_top":       f.IsTop,
		"display_time": SwapFieldUnix(f.DisplayTime.Unix()),
		"created_at":   f.CreatedAt.Unix(),
		"updated_at":   f.UpdatedAt.Unix(),
	}
	return data
}
