package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	TagTypeObject    def.EnumType = 10
	TagTypeDimension def.EnumType = 20
	TagTypeFunc      def.EnumType = 30

	DefaultParentId def.AutoIDType = 0
)

type Tag struct {
	ID        def.AutoIDType `gorm:"id"`
	AdminId   def.AutoIDType `gorm:"admin_id"`
	Type      def.EnumType   `gorm:"type"`
	ParentId  def.AutoIDType `gorm:"parent_id"`
	Name      string         `gorm:"name"`
	Status    def.EnumType   `gorm:"status"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (t *Tag) TableName() string {
	return "helix_dataset_tag"
}

// tag search
type TagCond struct {
	AdminId      def.AutoIDType
	Type         def.EnumType
	ParentIdList []def.AutoIDType
}

// 通过Id获取信息
func (t *Tag) GetById(ctx context.Context, id def.AutoIDType) (Tag, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id":     id,
		"status": StatusNew,
	}

	// 执行查询
	tagData := Tag{}
	err := db.Where(condWhere).First(&tagData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return tagData, err
	}
	return tagData, nil
}

// 通过name 获取信息
func (t *Tag) GetByName(ctx context.Context, name string) (Tag, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"name":   name,
		"status": StatusNew,
	}

	// 执行查询
	tagData := Tag{}
	err := db.Where(condWhere).First(&tagData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return tagData, err
	}
	return tagData, nil
}

// 通过Ids获取信息
func (t *Tag) GetByIds(ctx context.Context, tagIdList []def.AutoIDType) ([]Tag, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id":     tagIdList,
		"status": StatusNew,
	}

	// 执行查询
	var tagList []Tag
	err := db.Where(condWhere).Order("id asc").Find(&tagList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return tagList, err
}

// 添加 tag
func (t *Tag) Add(ctx context.Context, tagNew Tag) (Tag, error) {
	tagNew.Status = StatusNew
	tagNew.CreatedAt = time.Now()
	tagNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&tagNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return tagNew, err
}

// 获取 tag 列表
func (t *Tag) GetList(ctx context.Context, cond TagCond, limit, page def.IntType, orderBy ...string) ([]Tag, error) {
	db := resource.Gorm.WithContext(ctx)
	db = tagBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var tagList []Tag
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&tagList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return tagList, err
}

// 统计数据
func (t *Tag) CountByCond(ctx context.Context, cond TagCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = tagBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(t).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (t *Tag) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(t).Updates(&t).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (t *Tag) SwapData() map[string]any {
	var data map[string]any
	if t.ID <= 0 {
		return data
	}

	data = map[string]any{
		"id":         t.ID,
		"admin_id":   t.AdminId,
		"type":       t.Type,
		"name":       t.Name,
		"parent_id":  t.ParentId,
		"created_at": t.CreatedAt.Unix(),
		"updated_at": t.UpdatedAt.Unix(),
	}
	return data
}

// where build
func tagBuildWhere(db *gorm.DB, cond TagCond) *gorm.DB {
	if cond.AdminId > 0 {
		db = db.Where("admin_id = ?", cond.AdminId)
	}
	if cond.Type > 0 {
		db = db.Where("type = ?", cond.Type)
	}
	if len(cond.ParentIdList) > 0 {
		db = db.Where("parent_id in (?)", cond.ParentIdList)
	}
	db = db.Where("status <> ?", StatusDel)

	return db
}
