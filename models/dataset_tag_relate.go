package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

type DatasetTagRelate struct {
	ID        def.AutoIDType `gorm:"id"`
	DatasetId def.AutoIDType `gorm:"dataset_id"`
	TagId     def.AutoIDType `gorm:"tag_id"`
	Type      def.EnumType   `gorm:"type"`
	ParentId  def.AutoIDType `gorm:"parent_id"`
	CreatedAt time.Time      `gorm:"created_at"`
}

func (t *DatasetTagRelate) TableName() string {
	return "helix_dataset_tag_relate"
}

// tag search
type TagRelateCond struct {
	DatasetIdList []def.AutoIDType
	TagIdList     []def.AutoIDType
	ParentIdList  []def.AutoIDType
	MixIdList     []def.AutoIDType
}

// 通过Id获取信息
func (t *DatasetTagRelate) GetById(ctx context.Context, id def.AutoIDType) (DatasetTagRelate, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	tagData := DatasetTagRelate{}
	err := db.Where(condWhere).First(&tagData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return tagData, err
	}
	return tagData, nil
}

// 添加 tag
func (t *DatasetTagRelate) Add(ctx context.Context, tagRelateNew DatasetTagRelate) (DatasetTagRelate, error) {
	tagRelateNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&tagRelateNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return tagRelateNew, err
}

// 获取 tag 列表
func (t *DatasetTagRelate) GetList(ctx context.Context, cond TagRelateCond, limit def.IntType) ([]DatasetTagRelate, error) {
	db := resource.Gorm.WithContext(ctx)
	db = tagRelateBuildWhere(db, cond)

	// 执行查询
	var tagList []DatasetTagRelate
	err := db.Limit(limit).Find(&tagList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return tagList, err
}

// 统计数据
func (t *DatasetTagRelate) CountByCond(ctx context.Context, cond TagRelateCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = tagRelateBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(t).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

type tagSumRes struct {
	Field def.AutoIDType
	Total def.AutoIDType
}

func (t *DatasetTagRelate) GroupSum(ctx context.Context, cond TagRelateCond, field string) ([]tagSumRes, error) {
	db := resource.Gorm.WithContext(ctx)
	db = tagRelateBuildWhere(db, cond)

	// 执行查询
	var groupResultList []tagSumRes
	selectStr := field + " as field, count(distinct(dataset_id)) as total"
	err := db.Model(t).Select(selectStr).Group(field).Find(&groupResultList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return groupResultList, err
}

// 保存、修改
func (t *DatasetTagRelate) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(t).Updates(&t).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 删除
func (t *DatasetTagRelate) Delete(ctx context.Context, cond TagRelateCond) error {
	db := resource.Gorm.WithContext(ctx)
	db = tagRelateBuildWhere(db, cond)

	err := db.Delete(&t).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (t *DatasetTagRelate) SwapData() map[string]any {
	var tagData map[string]any
	if t.ID <= 0 {
		return tagData
	}

	tagData = map[string]any{
		"id":         t.ID,
		"type":       t.Type,
		"dataset_id": t.DatasetId,
		"tag_id":     t.TagId,
		"parent_id":  t.ParentId,
		"created_at": t.CreatedAt.Unix(),
	}
	return tagData
}

// where build
func tagRelateBuildWhere(db *gorm.DB, cond TagRelateCond) *gorm.DB {
	if len(cond.DatasetIdList) > 0 {
		db = db.Where("dataset_id in (?)", cond.DatasetIdList)
	}
	if len(cond.TagIdList) > 0 {
		db = db.Where("tag_id in (?)", cond.TagIdList)
	}
	if len(cond.MixIdList) > 0 {
		db = db.Where("(tag_id in (?) or parent_id in (?))", cond.MixIdList, cond.MixIdList)
	}
	if len(cond.ParentIdList) > 0 {
		db = db.Where("parent_id in (?)", cond.ParentIdList)
	}
	return db
}
