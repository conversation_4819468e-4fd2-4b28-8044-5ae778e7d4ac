package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	SourceTypePublic       def.EnumType = 10
	SourceTypeDocument     def.EnumType = 20
	SourceTypeFreeDatabase def.EnumType = 30
	SourceTypeMatch        def.EnumType = 40
	SourceTypePayDatabase  def.EnumType = 50
	SourceTypeSelf         def.EnumType = 60
	SourceTypeCustomer     def.EnumType = 70
	SourceTypePlatform     def.EnumType = 80
	SourceTypeOther        def.EnumType = 90

	IsLikeYes def.EnumType = 1
	IsLikeNo  def.EnumType = 0
)

var SourceTypeList = map[def.EnumType]bool{
	SourceTypePublic:       true,
	SourceTypeDocument:     true,
	SourceTypeFreeDatabase: true,
	SourceTypeMatch:        true,
	SourceTypePayDatabase:  true,
	SourceTypeSelf:         true,
	SourceTypeCustomer:     true,
	SourceTypePlatform:     true,
	SourceTypeOther:        true,
}

var SourceTypeMap = map[def.EnumType]string{
	SourceTypePublic:       "公共数据库",
	SourceTypeDocument:     "文献",
	SourceTypeFreeDatabase: "免费商业数据库",
	SourceTypeMatch:        "比赛数据",
	SourceTypePayDatabase:  "付费商业数据库",
	SourceTypeSelf:         "自行整理",
	SourceTypeCustomer:     "客户数据",
	SourceTypePlatform:     "平台数据",
	SourceTypeOther:        "其他",
}

// 表结构
type Dataset struct {
	ID           def.AutoIDType `gorm:"id"`
	AdminId      def.AutoIDType `gorm:"admin_id"`
	Name         string         `gorm:"name"`
	Version      string         `gorm:"version"`
	VersionDesc  string         `gorm:"version_desc"`
	ObjectIds    string         `gorm:"object_ids"`
	DimensionIds string         `gorm:"dimension_ids"`
	PurposeIds   string         `gorm:"purpose_ids"`
	SourceType   def.EnumType   `gorm:"source_type"`
	SourceConfig string         `gorm:"source_config"`
	Desc         string         `gorm:"desc"`
	Remark       string         `gorm:"remark"`
	MetricConfig string         `gorm:"metric_config"`
	FileUrl      string         `gorm:"file_url"`
	Size         float64        `gorm:"size"`
	Format       string         `gorm:"format"`
	Owner        string         `gorm:"owner"`
	IsLike       def.EnumType   `gorm:"is_like"`
	IsNew        def.EnumType   `gorm:"is_new"`
	BaseId       def.AutoIDType `gorm:"base_id"`
	CreatedAt    time.Time      `gorm:"created_at"`
	UpdatedAt    time.Time      `gorm:"updated_at"`
}

func (d *Dataset) TableName() string {
	return "helix_dataset"
}

// dataset search
type DatasetCond struct {
	IdList         []def.AutoIDType
	AdminId        def.AutoIDType
	Keyword        string
	Version        string
	SourceTypeList []def.EnumType
	BaseId         def.AutoIDType
	IsNew          bool
	LtCreatedAt    string
	GtCreatedAt    string
}

// 添加管理员
func (d *Dataset) Add(ctx context.Context, datasetNew Dataset) (Dataset, error) {
	datasetNew.CreatedAt = time.Now()
	datasetNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&datasetNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return datasetNew, err
}

// 通过Id获取信息
func (d *Dataset) GetById(ctx context.Context, datasetId def.AutoIDType) (Dataset, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": datasetId,
	}

	// 执行查询
	datasetData := Dataset{}
	err := db.Where(condWhere).First(&datasetData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return datasetData, err
	}
	return datasetData, nil
}

// 通过name获取信息
func (d *Dataset) GetByName(ctx context.Context, name string) (Dataset, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"name": name,
	}

	// 执行查询
	datasetData := Dataset{}
	err := db.Where(condWhere).First(&datasetData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return datasetData, err
	}
	return datasetData, nil
}

// 通过Ids获取信息
func (d *Dataset) GetByIds(ctx context.Context, datasetIdList []def.AutoIDType) ([]Dataset, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": datasetIdList,
	}

	// 执行查询
	var datasetList []Dataset
	err := db.Where(condWhere).Order("id asc").Find(&datasetList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return datasetList, err
}

// 批量获取数据
func (d *Dataset) GetList(ctx context.Context, cond DatasetCond, limit, page def.IntType, orderBy ...string) ([]Dataset, error) {
	db := resource.Gorm.WithContext(ctx)
	db = datasetBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 && len(orderBy[0]) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var datasetList []Dataset
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&datasetList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return datasetList, err
}

// 批量获取数据
func (d *Dataset) Count(ctx context.Context, cond DatasetCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = datasetBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(d).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

type SumRes struct {
	Field string
	Total def.CountType
	Num   float64
}

func (d *Dataset) Sum(ctx context.Context, cond DatasetCond) (SumRes, error) {
	db := resource.Gorm.WithContext(ctx)
	db = datasetBuildWhere(db, cond)

	// 执行查询
	res := SumRes{}
	err := db.Model(d).Select("count(*) as total, sum(size) as num").First(&res).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return res, err
}

func (d *Dataset) GroupSum(ctx context.Context, cond DatasetCond, field string) ([]SumRes, error) {
	db := resource.Gorm.WithContext(ctx)
	db = datasetBuildWhere(db, cond)

	// 执行查询
	var groupResultList []SumRes
	selectStr := field + " as field, count(*) as total, sum(size) as num"
	err := db.Model(d).Select(selectStr).Group(field).Find(&groupResultList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return groupResultList, err
}

// 修改用户数据
func (d *Dataset) Save(ctx context.Context, field ...string) error {
	db := resource.Gorm.WithContext(ctx)

	db = db.Model(d)
	if len(field) > 0 && len(field[0]) > 0 {
		db.Select(field)
	}
	err := db.Updates(&d).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 条件更新
func (d *Dataset) UpdateByCond(ctx context.Context, cond DatasetCond, updateData map[string]any) error {
	db := resource.Gorm.WithContext(ctx)
	db = datasetBuildWhere(db, cond)

	err := db.Model(d).Updates(updateData).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (d *Dataset) SwapData() map[string]any {
	var datasetData map[string]any
	if d.ID <= 0 {
		return datasetData
	}

	var objectIdList []def.AutoIDType
	_ = json.Unmarshal([]byte(d.ObjectIds), &objectIdList)

	var dimensionIdList []def.AutoIDType
	_ = json.Unmarshal([]byte(d.DimensionIds), &dimensionIdList)

	var purposeIdList []def.AutoIDType
	_ = json.Unmarshal([]byte(d.PurposeIds), &purposeIdList)

	var sourceConfig map[string]any
	_ = json.Unmarshal([]byte(d.SourceConfig), &sourceConfig)

	var metricConfig []map[string]any
	_ = json.Unmarshal([]byte(d.MetricConfig), &metricConfig)

	datasetData = map[string]any{
		"id":                d.ID,
		"admin_id":          d.AdminId,
		"name":              d.Name,
		"version":           d.Version,
		"version_desc":      d.VersionDesc,
		"object_id_list":    objectIdList,
		"dimension_id_list": dimensionIdList,
		"purpose_id_list":   purposeIdList,
		"source_type":       d.SourceType,
		"source_config":     sourceConfig,
		"desc":              d.Desc,
		"remark":            d.Remark,
		"metric_config":     metricConfig,
		"file_url":          d.FileUrl,
		"size":              d.Size,
		"format":            d.Format,
		"is_like":           d.IsLike,
		"is_new":            d.IsNew,
		"base_id":           d.BaseId,
		"created_at":        d.CreatedAt.Unix(),
		"updated_at":        d.UpdatedAt.Unix(),
	}
	return datasetData
}

// where build
func datasetBuildWhere(db *gorm.DB, cond DatasetCond) *gorm.DB {
	if len(cond.IdList) > 0 {
		db = db.Where("id in (?)", cond.IdList)
	}

	if cond.AdminId > 0 {
		db = db.Where("admin_id = ?", cond.AdminId)
	}

	if cond.BaseId > 0 {
		db = db.Where("base_id = ?", cond.BaseId)
	}

	if cond.IsNew == true {
		db = db.Where("is_new = ?", 1)
	}

	if len(cond.SourceTypeList) > 0 {
		db = db.Where("source_type in (?)", cond.SourceTypeList)
	}

	if len(cond.Keyword) > 0 {
		db = db.Where("(name like ? or `desc` like ?)", "%"+cond.Keyword+"%", "%"+cond.Keyword+"%")
	}

	if len(cond.Version) > 0 {
		db = db.Where("version = ?", cond.Version)
	}

	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at <= ?", cond.LtCreatedAt)
	}

	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at > ?", cond.GtCreatedAt)
	}

	return db
}
