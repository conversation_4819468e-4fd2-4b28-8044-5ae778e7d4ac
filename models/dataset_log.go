package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	OperateTypeAdd           def.EnumType = 10
	OperateTypeVersionUpdate def.EnumType = 20
	OperateTypeModify        def.EnumType = 30
	OperateTypeDownload      def.EnumType = 40
	OperateTypeManage        def.EnumType = 50 // 权限操作
)

// 表结构
type DatasetLog struct {
	ID          def.AutoIDType `gorm:"id"`
	AdminId     def.AutoIDType `gorm:"admin_id"`
	DatasetId   def.AutoIDType `gorm:"dataset_id"`
	OperateType def.EnumType   `gorm:"operate_type"`
	Content     string         `gorm:"content"`
	CreatedAt   time.Time      `gorm:"created_at"`
}

func (d *DatasetLog) TableName() string {
	return "helix_dataset_log"
}

// dataset log search
type DatasetLogCond struct {
	AdminId         def.AutoIDType
	DatasetId       def.AutoIDType
	OperateTypeList []def.EnumType
}

// 添加管理员
func (d *DatasetLog) Add(ctx context.Context, datasetLogNew DatasetLog) (DatasetLog, error) {
	datasetLogNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&datasetLogNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return datasetLogNew, err
}

// 通过Id获取信息
func (d *DatasetLog) GetById(ctx context.Context, datasetId def.AutoIDType) (Dataset, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": datasetId,
	}

	// 执行查询
	datasetData := Dataset{}
	err := db.Where(condWhere).First(&datasetData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return datasetData, err
	}
	return datasetData, nil
}

// 批量获取数据
func (d *DatasetLog) GetList(ctx context.Context, cond DatasetLogCond, limit, page def.IntType, orderBy ...string) ([]DatasetLog, error) {
	db := resource.Gorm.WithContext(ctx)
	db = datasetLogBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var datasetLogList []DatasetLog
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&datasetLogList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return datasetLogList, err
}

// 批量获取数据
func (d *DatasetLog) Count(ctx context.Context, cond DatasetLogCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = datasetLogBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(d).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 数据转化
func (d *DatasetLog) SwapData() map[string]any {
	var datasetData map[string]any
	if d.ID <= 0 {
		return datasetData
	}

	datasetData = map[string]any{
		"id":           d.ID,
		"admin_id":     d.AdminId,
		"dataset_id":   d.DatasetId,
		"operate_type": d.OperateType,
		"content":      d.Content,
		"created_at":   d.CreatedAt.Unix(),
	}
	return datasetData
}

// where build
func datasetLogBuildWhere(db *gorm.DB, cond DatasetLogCond) *gorm.DB {
	if cond.DatasetId > 0 {
		db = db.Where("dataset_id = ?", cond.DatasetId)
	}
	if cond.AdminId > 0 {
		db = db.Where("admin_id = ?", cond.AdminId)
	}
	if len(cond.OperateTypeList) > 0 {
		db = db.Where("operate_type in (?)", cond.OperateTypeList)
	}
	return db
}
