package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

type DocPic struct {
	ID        def.AutoIDType `gorm:"id"`
	FileUrl   string         `gorm:"file_url"`
	Remark    string         `gorm:"remark"`
	Status    def.EnumType   `gorm:"status"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (d *DocPic) TableName() string {
	return "manage_pic"
}

// 通过Id获取信息
func (d *DocPic) GetById(ctx context.Context, id def.AutoIDType) (DocPic, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id":     id,
		"status": StatusNew,
	}

	// 执行查询
	picData := DocPic{}
	err := db.Where(condWhere).First(&picData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return picData, err
	}
	return picData, nil
}

// 添加 doc pic
func (d *DocPic) Add(ctx context.Context, picNew DocPic) (DocPic, error) {
	picNew.Status = StatusNew
	picNew.CreatedAt = time.Now()
	picNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&picNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return picNew, err
}

// 获取 doc pic
func (d *DocPic) GetList(ctx context.Context, keyword string, limit, page def.IntType, orderBy ...string) ([]DocPic, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)
	db = db.Where("remark like ?", "%"+keyword+"%")

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var picList []DocPic
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&picList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return picList, err
}

// 统计数据
func (d *DocPic) CountByCond(ctx context.Context, keyword string) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)
	db = db.Where("remark like ?", "%"+keyword+"%")

	// 执行查询
	var count def.CountType
	err := db.Model(d).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (d *DocPic) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(d).Updates(&d).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (d *DocPic) SwapData() map[string]any {
	var tagData map[string]any
	if d.ID <= 0 {
		return tagData
	}

	tagData = map[string]any{
		"id":         d.ID,
		"file_url":   GetBosUrl(d.FileUrl),
		"status":     d.Status,
		"remark":     d.Remark,
		"created_at": d.CreatedAt.Unix(),
		"updated_at": d.UpdatedAt.Unix(),
	}
	return tagData
}
