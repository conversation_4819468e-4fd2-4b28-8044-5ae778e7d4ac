package models

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	NeedPushTrue  def.EnumType = 1
	NeedPushFalse def.EnumType = 0

	CostTypeCoupon  def.EnumType = 10
	CostTypeBilling def.EnumType = 20

	ChargeTypeNum      def.EnumType = 10
	ChargeTypeDuration def.EnumType = 20
)

// 表结构
type Bill struct {
	ID             def.AutoIDType `gorm:"id"`
	UserId         def.AutoIDType `gorm:"user_id"`
	RealId         def.AutoIDType `gorm:"real_id"`
	TaskId         def.AutoIDType `gorm:"task_id"`
	TaskType       def.EnumType   `gorm:"task_type"`
	ChargeType     def.EnumType   `gorm:"charge_type"`
	ConsoleApiId   def.AutoIDType `gorm:"console_api_id"`
	LogId          def.AutoIDType `gorm:"log_id"`
	StartTime      time.Time      `gorm:"start_time"`
	EndTime        time.Time      `gorm:"end_time"`
	ChargeNum      def.CountType  `gorm:"charge_num"`
	ChargeDuration def.CountType  `gorm:"charge_duration"`
	CostAmount     float64        `gorm:"cost_amount"`
	CouponIdList   string         `gorm:"coupon_id_list"`
	NeedPush       def.EnumType   `gorm:"need_push"`
	Level          def.CountType  `gorm:"level"`
	CreatedAt      time.Time      `gorm:"created_at"`
}

func (b *Bill) TableName() string {
	return "helix_bill"
}

// bill search
type BillCond struct {
	UserIdList       []def.AutoIDType
	FilterUserIdList []string
	TaskTypeList     []def.EnumType
	CostType         def.EnumType
	KeyId            def.AutoIDType
	GtCreatedAt      string
	LtCreatedAt      string
}

func (b *Bill) GetByCond(ctx context.Context, cond BillCond, limit, page def.IntType, orderBy ...string) ([]Bill, error) {
	db := resource.Gorm.WithContext(ctx)
	db = billBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var billList []Bill
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&billList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())

		return billList, err
	}
	return billList, nil
}

// 统计数据
func (b *Bill) CountByCond(ctx context.Context, cond BillCond, isDistinct bool) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = billBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	var err error
	if isDistinct {
		err = db.Model(b).Distinct("user_id").Count(&count).Error
	} else {
		err = db.Model(b).Count(&count).Error
	}
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

type BillSumRes struct {
	Total def.CountType
	Num   float64
}

func (b *Bill) Sum(ctx context.Context, cond BillCond) (BillSumRes, error) {
	db := resource.Gorm.WithContext(ctx)
	db = billBuildWhere(db, cond)

	// 执行查询
	res := BillSumRes{}
	err := db.Model(b).Select("count(*) as total, sum(cost_amount) as num").First(&res).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return res, err
}

// 数据转化
func (b *Bill) SwapData() map[string]any {
	billData := make(map[string]any)
	if b.ID <= 0 {
		return billData
	}

	// 处理数据
	billData = map[string]any{
		"bill_id":           b.ID,
		"user_id":           b.UserId,
		"task_id":           b.TaskId,
		"task_type":         b.TaskType,
		"cost_amount":       b.CostAmount,
		"charge_num":        b.ChargeNum,
		"charge_duration":   b.ChargeDuration,
		"charge_type":       b.ChargeType,
		"cost_type":         needPushToCostType(b.NeedPush),
		"level":             b.Level,
		"charge_start_time": SwapFieldUnix(b.StartTime.Unix()),
		"end_time":          SwapFieldUnix(b.EndTime.Unix()),
		"created_at":        b.CreatedAt.Unix(),
	}
	return billData
}

// where build
func billBuildWhere(db *gorm.DB, cond BillCond) *gorm.DB {
	if len(cond.TaskTypeList) > 0 {
		db = db.Where("(task_type in (?))", cond.TaskTypeList)
	}
	if cond.KeyId > 0 {
		db = db.Where("(user_id = ? OR task_id = ?)", cond.KeyId, cond.KeyId)
	}
	if len(cond.FilterUserIdList) > 0 {
		db = db.Where("(user_id not in (?))", cond.FilterUserIdList)
	}
	if cond.CostType > 0 {
		db = db.Where("(need_push = ?)", costTypeToNeedPush(cond.CostType))
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}
	return db
}

func costTypeToNeedPush(costType def.EnumType) def.EnumType {
	if costType == CostTypeCoupon {
		return NeedPushFalse
	}
	return NeedPushTrue
}

func needPushToCostType(needPush def.EnumType) def.EnumType {
	if needPush == NeedPushTrue {
		return CostTypeBilling
	}
	return CostTypeCoupon
}

func (b *Bill) GetByTaskID(ctx context.Context, taskID int64) (*Bill, error) {
	db := resource.Gorm.WithContext(ctx)
	billData := Bill{}
	err := db.Where("task_id = ?", taskID).First(&billData).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		go helpers.HelixNotice(ctx, err.Error())
		return nil, err
	}
	return &billData, nil
}

func (b *Bill) Add(ctx context.Context, billNew Bill) (Bill, error) {
	billNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&billNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return billNew, err
}

func (b *Bill) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(b).Updates(&b).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return err
}
