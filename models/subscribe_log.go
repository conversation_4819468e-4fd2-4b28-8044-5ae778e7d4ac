package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

type Subscribe struct {
	ID        def.AutoIDType `gorm:"id"`
	Email     string         `gorm:"email"`
	Content   string         `gorm:"content"`
	Status    def.EnumType   `gorm:"status"`
	CreatedAt time.Time      `gorm:"created_at"`
	UpdatedAt time.Time      `gorm:"updated_at"`
}

func (s *Subscribe) TableName() string {
	return "helix_subscribe_log"
}

// 获取列表
func (s *Subscribe) GetList(ctx context.Context, limit, page def.IntType, orderBy ...string) ([]Subscribe, error) {
	db := resource.Gorm.WithContext(ctx)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}
	condWhere := map[string]any{
		"status": StatusNew,
	}

	// 执行查询
	var subscribeList []Subscribe
	offset := limit * (page - 1)
	err := db.Where(condWhere).Limit(limit).Offset(offset).Order(order).Find(&subscribeList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return subscribeList, err
}

// 统计数据
func (s *Subscribe) CountByCond(ctx context.Context) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	condWhere := map[string]any{
		"status": StatusNew,
	}

	// 执行查询
	var count def.CountType
	err := db.Model(s).Where(condWhere).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 通过 email 获取
func (s *Subscribe) GetByEmail(ctx context.Context, email string) (Subscribe, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"email": email,
	}

	// 执行查询
	subscribeData := Subscribe{}
	err := db.Where(condWhere).First(&subscribeData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return subscribeData, err
	}
	return subscribeData, nil
}

// 数据转化
func (s *Subscribe) SwapData() map[string]any {
	var data map[string]any
	if s.ID <= 0 {
		return data
	}

	var conf map[string]any
	_ = json.Unmarshal([]byte(s.Content), &conf)

	data = map[string]any{
		"id":         s.ID,
		"status":     s.Status,
		"email":      s.Email,
		"config":     conf,
		"created_at": s.CreatedAt.Unix(),
		"updated_at": s.UpdatedAt.Unix(),
	}
	return data
}
