package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

// 10: mRNA药物研发 20：多肽/蛋白药物研发 30:小分子药物研发 40: 自定义
// 10: 疫苗设计 11: RNA二级结构预测 16: mRNA-自定义
const (
	FieldType1  = 1 // 历史项目
	FieldType10 = 10
	FieldType20 = 20
	FieldType30 = 30
	FieldType40 = 40

	CaseType1  = 1 // 历史项目
	CaseType10 = 10
	CaseType11 = 11
	CaseType16 = 16
)

var FieldTypeList = map[int]bool{
	FieldType10: true,
	FieldType20: true,
	FieldType30: true,
	FieldType40: true,
}

var CaseTypeList = map[int]bool{
	CaseType10: true,
	CaseType11: true,
	CaseType16: true,
}

type Project struct {
	ID           def.AutoIDType `gorm:"id"`
	UserId       def.AutoIDType `gorm:"user_id"`
	FieldType    def.EnumType   `gorm:"field_type"`
	CaseType     def.EnumType   `gorm:"case_type"`
	TaskTypeList string         `gorm:"task_type_list"`
	Name         string         `gorm:"name"`
	Intro        string         `gorm:"intro"`
	Status       def.EnumType   `gorm:"status"`
	Sort         def.EnumType   `gorm:"sort"`
	CreatedAt    time.Time      `gorm:"created_at"`
	UpdatedAt    time.Time      `gorm:"updated_at"`
}

func (p *Project) TableName() string {
	return "helix_project"
}

type ProjectCond struct {
	UserIdList       []def.AutoIDType
	FilterUserIdList []string
	CaseTypeList     []def.EnumType
	Keyword          string
	GtCreatedAt      string
	LtCreatedAt      string
}

func (p *Project) GetById(ctx context.Context, userId, id int64) (Project, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{}{
		"user_id": userId,
		"id":      id,
	}

	// 执行查询
	projectData := Project{}
	err := db.Where(condWhere).First(&projectData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return projectData, err
	}
	return projectData, nil
}

// 查询列表
func (p *Project) GetByCond(ctx context.Context, cond ProjectCond, limit, page def.IntType, orderBy ...string) ([]Project, error) {
	db := resource.Gorm.WithContext(ctx)
	db = projectBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var list []Project
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&list).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return list, err
}

// 统计数据
func (p *Project) CountByCond(ctx context.Context, cond ProjectCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = projectBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(p).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

func (p *Project) GetListByCase(ctx context.Context, userId int64, caseTypeList []int64, limit, page int) ([]Project, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	db = db.Where("status = ?", StatusNew)
	db = db.Where("user_id = ?", userId)
	if len(caseTypeList) > 0 {
		db = db.Where("case_type in ?", caseTypeList)
	}

	// 执行查询
	var dataList []Project
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order("sort, id desc").Find(&dataList).Error
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return dataList, err
}

// 保存项目
func (p *Project) Save(ctx context.Context) error {
	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(p).Updates(&p).Error
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

func (p *Project) Update(ctx context.Context) error {
	dataMap := map[string]any{
		"id":         p.ID,
		"sort":       p.Sort,
		"updated_at": time.Now(),
	}

	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(p).Updates(&dataMap).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (p *Project) SwapData() map[string]interface{} {
	var data map[string]interface{}
	if p.ID <= 0 {
		return data
	}

	var taskTypeList []int
	_ = json.Unmarshal([]byte(p.TaskTypeList), &taskTypeList)

	data = map[string]interface{}{
		"id":             p.ID,
		"user_id":        p.UserId,
		"field_type":     p.FieldType,
		"case_type":      p.CaseType,
		"task_type_list": taskTypeList,
		"name":           p.Name,
		"intro":          p.Intro,
		"status":         p.Status,
		"sort":           p.Sort,
		"created_at":     p.CreatedAt.Unix(),
		"updated_at":     p.UpdatedAt.Unix(),
	}
	return data
}

func projectBuildWhere(db *gorm.DB, cond ProjectCond) *gorm.DB {
	//db = db.Where("status <> ?", StatusDel)

	if len(cond.CaseTypeList) > 0 {
		db = db.Where("case_type in (?)", cond.CaseTypeList)
	}
	if len(cond.UserIdList) > 0 {
		db = db.Where("(user_id in (?))", cond.UserIdList)
	}
	if len(cond.FilterUserIdList) > 0 {
		db = db.Where("(user_id not in (?))", cond.FilterUserIdList)
	}
	if len(cond.Keyword) > 0 {
		db = db.Where("(id = ? OR name like ?)", cond.Keyword, "%"+cond.Keyword+"%")
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}
	return db
}
