package models

import (
	"context"
	"gorm.io/gorm"
	"icode.baidu.com/helix_web/library/def"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

type ActionLog struct {
	ID        def.AutoIDType `gorm:"id"`
	AdminId   def.AutoIDType `gorm:"admin_id"`
	Params    string         `gorm:"params"`
	Uri       string         `gorm:"uri"`
	CreatedAt time.Time      `gorm:"created_at"`
}

func (a *ActionLog) TableName() string {
	return "manage_action_log"
}

// 通过Id获取信息
func (a *ActionLog) GetById(ctx context.Context, id def.AutoIDType) (ActionLog, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	actionLogData := ActionLog{}
	err := db.Where(condWhere).First(&actionLogData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())
		return actionLogData, err
	}
	return actionLogData, nil
}

// 添加 ActionLog
func (a *ActionLog) Add(ctx context.Context, actionLogNew ActionLog) (ActionLog, error) {
	actionLogNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&actionLogNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return actionLogNew, err
}

// 获取 ActionLog 列表
func (a *ActionLog) GetList(ctx context.Context, limit, page def.IntType, orderBy ...string) ([]ActionLog, error) {
	db := resource.Gorm.WithContext(ctx)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var actionLogList []ActionLog
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&actionLogList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return actionLogList, err
}

// 数据转化
func (a *ActionLog) SwapData() map[string]any {
	var actionLogData map[string]any
	if a.ID <= 0 {
		return actionLogData
	}

	actionLogData = map[string]any{
		"id":         a.ID,
		"admin_id":   a.AdminId,
		"uri":        a.Uri,
		"params":     a.Params,
		"created_at": a.CreatedAt.Unix(),
	}
	return actionLogData
}
