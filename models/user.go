package models

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	UserTypeInside def.EnumType = 1
	UserTypeNormal def.EnumType = 10
	UserTypeCharge def.EnumType = 20
	UserTypeTry    def.EnumType = 30

	SourcePassport def.EnumType = 10
	SourceUC       def.EnumType = 11
	SourceGitHub   def.EnumType = 20

	DiscountStatusNone      def.EnumType = 0
	DiscountStatusAvailable def.EnumType = 1
	DiscountStatusExpired   def.EnumType = 2

	DefaultInnerRealID int = 6413248418 // 线上

	UserPriorityLow = "low"
	UserPriorityMid = "mid"
)

const DefaultCameoRealId def.AutoIDType = 4969117260

var UserPriorityToQos = map[string]string{
	UserPriorityLow: "mid_low",
	UserPriorityMid: "normal",
}

// 表结构
type User struct {
	ID                uint64    `gorm:"id"`
	Username          string    `gorm:"username"`
	DisplayName       string    `gorm:"display_name"`
	RealID            uint64    `gorm:"real_id"`
	UserID            string    `gorm:"user_id"`
	UserDomainID      string    `gorm:"user_domain_id"`
	Type              uint64    `gorm:"type"`
	Email             string    `gorm:"email"`
	Phone             string    `gorm:"phone"`
	Source            uint64    `gorm:"source"`
	NeedSwitch        uint64    `gorm:"need_switch"`
	Priority          string    `gorm:"priority"`
	Discount          int       `gorm:"discount"`
	DiscountEndTime   time.Time `gorm:"discount_end_time"`
	ConsoleOpen       bool      `gorm:"console_open"`
	ConsoleChangeTime time.Time `gorm:"console_change_time"`
	ChpcOpen          bool      `gorm:"chpc_open"`
	ChpcChangeTime    time.Time `gorm:"chpc_change_time"`
	Remark            string    `gorm:"remark"`
	LoginTime         time.Time `gorm:"login_time"`
	ChangeTime        time.Time `gorm:"change_time"`
	CreatedAt         time.Time `gorm:"created_at"`
	UpdatedAt         time.Time `gorm:"updated_at"`
}

// user search
type UserCond struct {
	UserIdList           []def.AutoIDType
	Type                 def.EnumType
	Keyword              []string
	UsernameList         []string
	FilterUserIdList     []string
	GtCreatedAt          string
	LtCreatedAt          string
	GtActiveAt           string
	LtActiveAt           string
	MinChangeAt          string
	MaxChangeAt          string
	MinChpcChangeTime    string
	MaxChpcChangeTime    string
	MinConsoleChangeTime string
	MaxConsoleChangeTime string
	Remark               string
	Priority             []string
	Discount             []int
}

func (u *User) TableName() string {
	return "helix_user"
}

// 获取列表
func (u *User) GetList(ctx context.Context, cond UserCond, limit, page def.IntType, orderBy ...string) ([]User, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return nil, err
	}
	db := resource.Gorm.WithContext(ctx)
	db = userBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var userList []User
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&userList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return userList, err
}

// 获取全部列表
func (u *User) GetAllList(ctx context.Context, cond UserCond) ([]User, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return nil, err
	}
	db := resource.Gorm.WithContext(ctx)
	db = userBuildWhere(db, cond)

	// 执行查询
	var userList []User
	limit := 1000
	var lastID uint64 = 0
	for {
		var users []User
		if err := db.Where("id > ?", lastID).Order("id desc").Limit(limit).Find(&users).Error; err != nil {
			go helpers.HelixNotice(ctx, err.Error())
			return nil, err
		}
		if len(users) == 0 {
			break
		}
		userList = append(userList, users...)
		lastID = users[len(users)-1].ID
	}
	return userList, nil
}

// 统计数据
func (u *User) Count(ctx context.Context, cond UserCond) (def.CountType, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return 0, err
	}

	db := resource.Gorm.WithContext(ctx)
	db = userBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(u).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

type UserSumRes struct {
	Date time.Time
	Sum  def.CountType
}

// 统计数据
func (u *User) SumByCond(ctx context.Context, cond UserCond) ([]UserSumRes, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return nil, err
	}
	db := resource.Gorm.WithContext(ctx)
	db = userBuildWhere(db, cond)

	// 执行查询
	var res []UserSumRes
	err := db.Model(u).Select("Date(created_at) date, count(id) as sum").Group("date").Find(&res).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return res, err
}

// 通过Id获取用户信息
func (u *User) GetByIds(ctx context.Context, userIdList []def.AutoIDType) ([]User, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return nil, err
	}
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": userIdList,
	}

	// 执行查询
	var userList []User
	err := db.Where(condWhere).Order("id asc").Find(&userList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return userList, err
}

// 通过realId获取用户信息
func (u *User) GetByRealIds(ctx context.Context, realIdList []def.AutoIDType) ([]User, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return nil, err
	}
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"real_id": realIdList,
	}

	// 执行查询
	var userList []User
	err := db.Where(condWhere).Order("id asc").Find(&userList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return userList, err
}

// 获取用户信息
func (u *User) GetUserByUsername(ctx context.Context, username string) (User, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return User{}, err
	}
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"username": username,
	}

	// 执行查询
	userData := User{}
	err := db.Where(condWhere).First(&userData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return userData, err
	}
	return userData, nil
}

// 获取用户信息
func (u *User) GetUserById(ctx context.Context, userId def.AutoIDType) (User, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return User{}, err
	}
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": userId,
	}

	// 执行查询
	userData := User{}
	err := db.Where(condWhere).First(&userData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return userData, err
	}
	return userData, nil
}

// 获取用户信息
func (u *User) GetUserByRealId(ctx context.Context, realId def.AutoIDType) (User, error) {
	if err := u.refreshDiscount(ctx); err != nil {
		return User{}, err
	}
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"real_id": realId,
	}

	// 执行查询
	userData := User{}
	err := db.Where(condWhere).First(&userData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return userData, err
	}
	return userData, nil
}

// 修改用户数据
func (u *User) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(u).Updates(&u).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

func (u *User) refreshDiscount(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(u).Where("discount = 1").Where("discount_end_time <= ?",
		helpers.UnixToTimeStr(time.Now().Unix())).Update("discount", 2).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (u *User) SwapData() map[string]any {
	var data map[string]any
	if u.ID <= 0 {
		return data
	}

	data = map[string]any{
		"user_id":             u.ID,
		"real_id":             u.RealID,
		"username":            u.Username,
		"display_name":        u.DisplayName,
		"email":               u.Email,
		"phone":               u.Phone,
		"remark":              u.Remark,
		"source":              u.Source,
		"active_time":         SwapFieldUnix(u.LoginTime.Unix()),
		"change_time":         SwapFieldUnix(u.ChangeTime.Unix()),
		"created_at":          u.CreatedAt.Unix(),
		"updated_at":          u.UpdatedAt.Unix(),
		"chpc_open":           u.ChpcOpen,
		"chpc_change_time":    SwapFieldUnix(u.ChpcChangeTime.Unix()),
		"console_open":        u.ConsoleOpen,
		"console_change_time": SwapFieldUnix(u.ConsoleChangeTime.Unix()),
		"priority":            u.Priority,
		"discount":            u.Discount,
		"discount_end_time":   SwapFieldUnix(u.DiscountEndTime.Unix()),
		"type":                u.Type,
	}
	return data
}

// where build
func userBuildWhere(db *gorm.DB, cond UserCond) *gorm.DB {
	fmt.Println(cond)
	if cond.Type != 0 {
		db = db.Where(" type = ? ", cond.Type)
	}
	if len(cond.UserIdList) > 0 {
		db = db.Where("(id in (?))", cond.UserIdList)
	}
	if len(cond.Discount) > 0 {
		db = db.Where(" discount in ? ", cond.Discount)
	}
	if len(cond.FilterUserIdList) > 0 {
		db = db.Where("(id not in (?))", cond.FilterUserIdList)
	}
	if len(cond.Keyword) > 0 {
		sql := " id in ? "
		args := []any{cond.Keyword}
		for _, keyword := range cond.Keyword {
			sql += " OR display_name like ? OR username like ? "
			args = append(args, "%"+keyword+"%")
			args = append(args, "%"+keyword+"%")
		}
		sql = "(" + sql + ")"
		db = db.Where(sql, args...)
	}
	if len(cond.Remark) > 0 {
		db = db.Where(" remark like ? ", "%"+cond.Remark+"%")
	}
	if len(cond.UsernameList) > 0 {
		db = db.Where("(username in (?))", cond.UsernameList)
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}
	if len(cond.GtActiveAt) > 0 {
		db = db.Where("login_time >= ?", cond.GtActiveAt)
	}
	if len(cond.LtActiveAt) > 0 {
		db = db.Where("login_time < ?", cond.LtActiveAt)
	}
	if len(cond.MinChangeAt) > 0 {
		db = db.Where("change_time >= ?", cond.MinChangeAt)
	}
	if len(cond.MaxChangeAt) > 0 {
		db = db.Where("change_time < ?", cond.MaxChangeAt)
	}
	if len(cond.MinChpcChangeTime) > 0 {
		db = db.Where("chpc_change_time >= ?", cond.MinChpcChangeTime)
	}
	if len(cond.MaxChpcChangeTime) > 0 {
		db = db.Where("chpc_change_time < ?", cond.MaxChpcChangeTime)
	}
	if len(cond.MinConsoleChangeTime) > 0 {
		db = db.Where("console_change_time >= ?", cond.MinConsoleChangeTime)
	}
	if len(cond.MaxConsoleChangeTime) > 0 {
		db = db.Where("console_change_time < ?", cond.MaxConsoleChangeTime)
	}
	return db
}
