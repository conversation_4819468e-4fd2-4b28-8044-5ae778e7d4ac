package models

import (
	"strings"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/def"
)

const (
	StatusNew    def.EnumType = 10
	StatusOnline def.EnumType = 20
	StatusDel    def.EnumType = -1
)

func SwapFieldUnix(timeUnix def.TimestampType) def.TimestampType {
	if timeUnix > 0 {
		return timeUnix
	}

	return def.TimestampType(0)
}

// GetBosUrl 获取 bos 地址
func GetBosUrl(bosPath string) string {
	bucket, object := helpers.DealBosFileUrl(bosPath)

	bosUrl, _ := bce.GenerateObjectUrl(bucket, object, bce.ObjectUrlExpireTime)
	if bosUrl != "" {
		index := strings.Index(bosUrl, "?")
		if index > 0 {
			return bosUrl[:index]
		}
	}

	return bosUrl
}
