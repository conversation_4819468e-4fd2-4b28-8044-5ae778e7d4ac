package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

type Coop struct {
	ID           def.AutoIDType `gorm:"id"`
	UserId       def.AutoIDType `gorm:"user_id"`
	AdminId      def.AutoIDType `gorm:"admin_id"`
	CoopType     def.AutoIDType `gorm:"coop_type"`
	ProductList  string         `gorm:"product_list"`
	Demand       string         `gorm:"demand"`
	CustomerType def.EnumType   `gorm:"customer_type"`
	Industry     def.EnumType   `gorm:"industry"`
	Company      string         `gorm:"company"`
	BudgetType   def.EnumType   `gorm:"budget_type"`
	Name         string         `gorm:"name"`
	Position     string         `gorm:"position"`
	Phone        string         `gorm:"phone"`
	Email        string         `gorm:"email"`
	Status       def.EnumType   `gorm:"status"`
	Remark       string         `gorm:"remark"`
	DealTime     time.Time      `gorm:"deal_time"`
	CreatedAt    time.Time      `gorm:"created_at"`
	UpdatedAt    time.Time      `gorm:"updated_at"`
}

func (c *Coop) TableName() string {
	return "helix_cooperation"
}

// 通过 Id 获取信息
func (c *Coop) GetById(ctx context.Context, id def.AutoIDType) (Coop, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	coopData := Coop{}
	err := db.Where(condWhere).First(&coopData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return coopData, err
	}
	return coopData, nil
}

// 获取 coop 列表
func (c *Coop) GetList(ctx context.Context, cond CoopCond, limit, page def.IntType, orderBy ...string) ([]Coop, error) {
	db := resource.Gorm.WithContext(ctx)
	db = coopBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var coopList []Coop
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&coopList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return coopList, err
}

// 统计数据
func (c *Coop) CountByCond(ctx context.Context, cond CoopCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	db = coopBuildWhere(db, cond)

	// 执行查询
	var count def.CountType
	err := db.Model(c).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (c *Coop) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(c).Updates(&c).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (c *Coop) SwapData() map[string]any {
	var data map[string]any
	if c.ID <= 0 {
		return data
	}

	var productList []string
	_ = json.Unmarshal([]byte(c.ProductList), &productList)

	data = map[string]any{
		"id":            c.ID,
		"user_id":       c.UserId,
		"admin_id":      c.AdminId,
		"coop_type":     c.CoopType,
		"product_list":  productList,
		"demand":        c.Demand,
		"customer_type": c.CustomerType,
		"industry":      c.Industry,
		"company":       c.Company,
		"budget_type":   c.BudgetType,
		"name":          c.Name,
		"position":      c.Position,
		"phone":         c.Phone,
		"email":         c.Email,
		"status":        c.Status,
		"remark":        c.Remark,
		"deal_name":     SwapFieldUnix(c.DealTime.Unix()),
		"created_at":    c.CreatedAt.Unix(),
		"updated_at":    c.UpdatedAt.Unix(),
	}
	return data
}

// CoopCond user search
type CoopCond struct {
	CoopType     def.EnumType
	BudgetType   def.EnumType
	CustomerType def.EnumType
	ProductList  []string
	MinTime      string
	MaxTime      string
}

// where build
func coopBuildWhere(db *gorm.DB, cond CoopCond) *gorm.DB {
	if cond.CoopType > 0 {
		db = db.Where("coop_type = ?", cond.CoopType)
	}
	if cond.BudgetType > 0 {
		db = db.Where("budget_type = ?", cond.BudgetType)
	}
	if cond.CustomerType > 0 {
		db = db.Where("customer_type = ?", cond.CustomerType)
	}
	if len(cond.MinTime) > 0 {
		db = db.Where("created_at >= ?", cond.MinTime)
	}
	if len(cond.MaxTime) > 0 {
		db = db.Where("created_at < ?", cond.MaxTime)
	}
	return db
}
