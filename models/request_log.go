package models

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const logLimit def.IntType = 1000

// RequestLog 表结构
type RequestLog struct {
	ID        def.AutoIDType `gorm:"id"`
	UserId    def.AutoIDType `gorm:"user_id"`
	Uri       string         `gorm:"uri"`
	CreatedAt time.Time      `gorm:"created_at"`
}

func (r *RequestLog) TableName() string {
	return "helix_request_log"
}

type RequestCond struct {
	GtCreatedAt string
	LtCreatedAt string
}
type logRes struct {
	UserId def.AutoIDType `gorm:"user_id"`
	Total  def.CountType  `gorm:"total"`
}

func (r *RequestLog) GetList(ctx context.Context, cond RequestCond) ([]logRes, error) {
	db := resource.Gorm.WithContext(ctx)

	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}

	// 执行查询
	var logList []logRes
	err := db.Model(r).Limit(logLimit).Select("user_id, count(id) as total").Group("user_id").Find(&logList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return logList, err
}

func (r *RequestLog) CountByCond(ctx context.Context, cond RequestCond) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}

	// 执行查询
	var count def.CountType
	err := db.Model(r).Distinct("user_id").Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

type ReqSumRes struct {
	Date time.Time
	Sum  def.CountType
}

func (r *RequestLog) SumByCond(ctx context.Context, cond RequestCond) ([]ReqSumRes, error) {
	db := resource.Gorm.WithContext(ctx)
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at >= ?", cond.GtCreatedAt)
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}

	// 执行查询
	var res []ReqSumRes
	err := db.Model(r).Select("Date(created_at) date, count(distinct(user_id)) as sum").Group("date").Find(&res).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return res, err
}
