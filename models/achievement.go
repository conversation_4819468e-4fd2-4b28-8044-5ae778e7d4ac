package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

type Achievement struct {
	ID          def.AutoIDType `gorm:"id"`
	AdminId     def.AutoIDType `gorm:"admin_id"`
	Title       string         `gorm:"title"`
	Author      string         `gorm:"author"`
	Url         string         `gorm:"url"`
	TagList     string         `gorm:"tag_list"`
	TagListEn   string         `gorm:"tag_list_en"`
	Status      def.EnumType   `gorm:"status"`
	PublishTime time.Time      `gorm:"publish_time"`
	CreatedAt   time.Time      `gorm:"created_at"`
	UpdatedAt   time.Time      `gorm:"updated_at"`
}

func (a *Achievement) TableName() string {
	return "helix_achievement"
}

// 通过Id获取信息
func (a *Achievement) GetById(ctx context.Context, id def.AutoIDType) (Achievement, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	achievementData := Achievement{}
	err := db.Where(condWhere).First(&achievementData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return achievementData, err
	}
	return achievementData, nil
}

// 添加achievement
func (a *Achievement) Add(ctx context.Context, achievementNew Achievement) (Achievement, error) {
	achievementNew.Status = StatusNew
	achievementNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&achievementNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return achievementNew, err
}

// 获取 achievement 列表
func (a *Achievement) GetList(ctx context.Context, limit, page def.IntType, orderBy ...string) ([]Achievement, error) {
	db := resource.Gorm.WithContext(ctx)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var achievementList []Achievement
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&achievementList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return achievementList, err
}

// 统计数据
func (a *Achievement) CountByCond(ctx context.Context) (def.CountType, error) {
	db := resource.Gorm.WithContext(ctx)

	// 执行查询
	var count def.CountType
	err := db.Model(a).Count(&count).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 保存、修改
func (a *Achievement) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(a).Updates(&a).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (a *Achievement) SwapData() map[string]any {
	var data map[string]any
	if a.ID <= 0 {
		return data
	}

	var tagList, tagListEn []string
	_ = json.Unmarshal([]byte(a.TagList), &tagList)
	_ = json.Unmarshal([]byte(a.TagListEn), &tagListEn)

	data = map[string]any{
		"id":           a.ID,
		"admin_id":     a.AdminId,
		"title":        a.Title,
		"author":       a.Author,
		"url":          a.Url,
		"tag_list":     tagList,
		"tag_list_en":  tagListEn,
		"status":       a.Status,
		"publish_time": SwapFieldUnix(a.PublishTime.Unix()),
		"created_at":   a.CreatedAt.Unix(),
		"updated_at":   a.UpdatedAt.Unix(),
	}
	return data
}
