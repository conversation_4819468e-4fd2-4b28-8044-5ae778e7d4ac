package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

// 表结构
type Menu struct {
	ID            def.AutoIDType `gorm:"id"`
	Name          string         `gorm:"name"`
	ParentId      def.AutoIDType `gorm:"parent_id"`
	PermissionIds string         `gorm:"permission_ids"`
	CreatedAt     time.Time      `gorm:"created_at"`
	UpdatedAt     time.Time      `gorm:"updated_at"`
}

func (m *Menu) TableName() string {
	return "manage_menu"
}

// 添加 role
func (m *Menu) Add(ctx context.Context, menuNew Menu) (Menu, error) {
	menuNew.CreatedAt = time.Now()
	menuNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&menuNew).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}

	return menuNew, err
}

// 通过id获取
func (m *Menu) GetById(ctx context.Context, id def.AutoIDType) (Menu, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	menuData := Menu{}
	err := db.Where(condWhere).First(&menuData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return menuData, err
	}
	return menuData, nil
}

// 通过ids获取
func (m *Menu) GetByIds(ctx context.Context, ids []def.AutoIDType) ([]Menu, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": ids,
	}

	// 执行查询
	var menuList []Menu
	err := db.Where(condWhere).Find(&menuList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return menuList, err
}

// 获取所有数据
func (m *Menu) GetList(ctx context.Context) ([]Menu, error) {
	db := resource.Gorm.WithContext(ctx)

	// 执行查询
	var menuList []Menu
	err := db.Order("id asc").Find(&menuList).Error
	if err != nil {
		go helpers.HelixNotice(ctx, err.Error())
	}
	return menuList, err
}

// 数据转化
func (m *Menu) SwapData() map[string]any {
	var data map[string]any
	if m.ID <= 0 {
		return data
	}

	data = map[string]any{
		"id":         m.ID,
		"name":       m.Name,
		"parent_id":  m.ParentId,
		"created_at": m.CreatedAt.Unix(),
		"updated_at": m.UpdatedAt.Unix(),
	}
	return data
}
