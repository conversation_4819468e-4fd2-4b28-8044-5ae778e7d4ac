package bootstrap

import (
	"context"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/gorm_adapter"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/mysql"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/net/servicer"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/helix_web/library/resource"
)

// MustInit 组件初始化，若失败，会panic
func MustInit(ctx context.Context) {
	initLoggers(ctx)

	initRal(ctx)

	loadServicer(ctx)

	initMySQL(ctx)

	initRedis(ctx)
}

// initLoggers 初始化logger
func initLoggers(ctx context.Context) {
	{
		webLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/service.toml"))
		if err != nil {
			panic(err.Error())
		}
		resource.LoggerService = webLogger

		TryRegisterCloser(webLogger) // 注册closer,这样可以保证程序进程退出前，日志全部落盘
	}
}

// initRal 初始化服务配置 以及ral组件（日志...）
func initRal(ctx context.Context) {
	_ = ral.InitDefault(ctx)
}

// loadServicer 加载服务配置
func loadServicer(ctx context.Context) {
	pattern := filepath.Join(env.ConfDir(), "servicer", "*.toml")
	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
}

// 初始化mysql
func initMySQL(_ context.Context) {
	// client 初始化为单例，放到 resource 里去
	resource.MySQLClient = mustInitOneMySQL("mysql")
	gorm, err := gorm_adapter.NewGorm(resource.MySQLClient, gorm_adapter.OptLogger(resource.LoggerService))
	if err != nil {
		panic(err.Error())
	}

	resource.Gorm = gorm
}
func mustInitOneMySQL(name interface{}) mysql.Client {
	opts := []mysql.ClientOption{
		mysql.OptObserver(mysql.NewMetricsObserverFunc(nil)),
	}
	client, err := mysql.NewClient(name, opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return client
}

// initRedis 初始化redis
func initRedis(_ context.Context) {
	// client 初始化为单例，放到 resource 里去
	resource.RedisClient = mustInitOneRedis("redis")
}
func mustInitOneRedis(name string) redis.Client {
	opts := []redis.ClientOption{
		redis.OptHooker(redis.NewMetricsHook(nil)),
		redis.OptHooker(redis.NewLogHook()),
	}

	client, err := redis.NewClient(name, opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return client
}
