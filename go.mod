module icode.baidu.com/helix_web

go 1.18

require (
	github.com/afocus/captcha v0.0.0-20191010092841-4bd1f21c8868
	github.com/baidubce/bce-sdk-go v0.9.52
	github.com/go-playground/validator/v10 v10.16.0
	github.com/go-redis/redis/v8 v8.6.0
	github.com/google/uuid v1.0.0
	github.com/jordan-wright/email v4.0.0+incompatible
	github.com/pelletier/go-toml v1.9.5
	gorm.io/gorm v1.20.11
	icode.baidu.com/baidu/bce-iam/sdk-go v1.2.11
	icode.baidu.com/baidu/gdp/automaxprocs v0.1.************
	icode.baidu.com/baidu/gdp/codec v1.20.0
	icode.baidu.com/baidu/gdp/conf v1.20.0
	icode.baidu.com/baidu/gdp/env v1.20.0
	icode.baidu.com/baidu/gdp/ghttp v1.20.11
	icode.baidu.com/baidu/gdp/gorm_adapter v1.20.1
	icode.baidu.com/baidu/gdp/logit v1.20.11
	icode.baidu.com/baidu/gdp/mysql v1.20.6
	icode.baidu.com/baidu/gdp/net v1.20.10
	icode.baidu.com/baidu/gdp/redis v1.20.8
)

require (
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/didi/gendry v1.5.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.2 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-sql-driver/mysql v1.5.0 // indirect
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0 // indirect
	github.com/golang/protobuf v1.4.3 // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.7 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.1 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/prometheus/client_golang v1.9.0 // indirect
	github.com/prometheus/client_model v0.2.0 // indirect
	github.com/prometheus/common v0.15.0 // indirect
	github.com/prometheus/procfs v0.3.0 // indirect
	go.opentelemetry.io/otel v0.17.0 // indirect
	go.opentelemetry.io/otel/metric v0.17.0 // indirect
	go.opentelemetry.io/otel/trace v0.17.0 // indirect
	golang.org/x/crypto v0.7.0 // indirect
	golang.org/x/image v0.0.0-20190802002840-cff245a6509b // indirect
	golang.org/x/net v0.8.0 // indirect
	golang.org/x/sys v0.6.0 // indirect
	golang.org/x/text v0.8.0 // indirect
	golang.org/x/time v0.0.0-20201208040808-7e3f01d25324 // indirect
	google.golang.org/protobuf v1.25.0 // indirect
	gorm.io/driver/mysql v1.0.3 // indirect
	icode.baidu.com/baidu/gdp/bns v1.20.3 // indirect
	icode.baidu.com/baidu/gdp/exjson v0.0.0-20190802111509-ad9978e27629 // indirect
	icode.baidu.com/baidu/gdp/extension v1.20.7 // indirect
	icode.baidu.com/baidu/gdp/mcpack v1.0.0 // indirect
)
