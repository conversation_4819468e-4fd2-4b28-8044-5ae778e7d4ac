package services

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/models"
)

func CheckTrial(ctx context.Context) error {
	trialM := models.Trial{}
	trialList, err := trialM.GetTrail(ctx, 0, nil, nil, 500, 1)
	if err != nil {
		return err
	}

	for _, trial := range trialList {
		if trial.Type == models.TrialTypeTime && trial.EndTime.Unix() < time.Now().Unix() {
			trial.Status = models.TrialStatusLose
			_ = trial.Save(ctx)
			continue
		}

		if trial.Type == models.TrialTypeNum && trial.LimitNum <= trial.UsedNum {
			trial.Status = models.TrialStatusLose
			_ = trial.Save(ctx)
		}
	}
	return nil
}

func CheckCoupon(ctx context.Context) error {
	couponM := models.Coupon{}
	couponList, err := couponM.GetList(ctx, 0, nil, nil, 500, 1)
	if err != nil {
		return err
	}

	for _, coupon := range couponList {
		if coupon.EndTime.Unix() < time.Now().Unix() {
			coupon.Status = models.TrialStatusLose
			_ = coupon.Save(ctx)
			continue
		}

	}
	return nil
}
