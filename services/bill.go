package services

import (
	"context"
	"encoding/json"
	"errors"
	"math"
	"strconv"

	"icode.baidu.com/helix_web/library/def"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

const (
	Level1 = 1
	Level2 = 2
	Level3 = 3

	AdmetForecastPrice   = 0.5
	KYKTPrice            = 1.1
	UTRPrice             = 2000
	LinearFoldPrice      = 50
	LinearPartitionPrice = 50
)

var RNAVersionToLevelMap = map[string]int{
	"basic":   Level1,
	"plus":    Level2,
	"advance": Level3,
}
var RNALevelToPriceMap = map[int]float64{
	Level1: 2000,
	Level2: 2400,
	Level3: 2500,
}
var ProteinLevelToPriceMap = map[int]float64{
	Level1: 0.25,
	Level2: 0.3,
	Level3: 60,
}
var ProteinSingleLevelToPriceMap = map[int]float64{
	Level1: 0.35,
	Level2: 0.45,
	Level3: 200,
}
var ProteinComplexLevelToPriceMap = map[int]float64{
	Level1: 0.35,
	Level2: 0.4,
	Level3: 0.4,
}
var RNALevelToIdMap = map[int]int{
	Level1: LinearDesign1,
	Level2: LinearDesign2,
	Level3: LinearDesign3,
}
var ProteinLevelToIdMap = map[int]int{
	Level1: HelixFold1,
	Level2: HelixFold2,
	Level3: HelixFold3,
}
var ProteinSingleLevelToIdMap = map[int]int{
	Level1: HelixFoldSingle1,
	Level2: HelixFoldSingle2,
	Level3: HelixFoldSingle3,
}
var ProteinComplexLevelToIdMap = map[int]int{
	Level1: ProteinComplex1,
	Level2: ProteinComplex2,
	Level3: ProteinComplex2,
}
var LevelToSpecsMap = map[string]string{
	"20-1": "basic",
	"20-2": "plus",
	"20-3": "advance",
	"60-1": "序列长度11-1000",
	"60-2": "序列长度1000-3000",
	"60-3": "序列长度大于3000",
	"61-1": "序列长度11-1000",
	"61-2": "序列长度1000-3000",
	"61-3": "序列长度大于3000",
	"65-1": "序列长度13-2000",
	"65-2": "序列长度2001-4000",
	"30-1": "-",
	"93-1": "序列长度0-2000",
	"25-1": "-",
	"10-1": "-",
	"11-1": "-",
}
var LevelToApiIdMap = map[string]int{
	"20-1": LinearDesign1,
	"20-2": LinearDesign2,
	"20-3": LinearDesign3,
	"60-1": HelixFold1,
	"60-2": HelixFold2,
	"60-3": HelixFold3,
	"61-1": HelixFoldSingle1,
	"61-2": HelixFoldSingle2,
	"61-3": HelixFoldSingle3,
	"65-1": ProteinComplex1,
	"65-2": ProteinComplex2,
	"30-1": AdmetForecast,
	"93-1": KYKT,
	"25-1": UTR,
	"10-1": LinearFold,
	"11-1": LinearPartition,
}

// 处理计费
func DoCharge(ctx context.Context, taskData models.Task) bool {
	if taskData.ChargeTab != def.EnumType(models.ChargeTabCoupon) &&
		taskData.ChargeTab != def.EnumType(models.ChargeTabBilling) {
		return false
	}

	var taskConf models.PushBillingTaskConfig
	_ = json.Unmarshal([]byte(taskData.Config), &taskConf)

	// 获取信息
	var price float64
	var consoleApiId int
	taskType := int(taskData.Type)
	switch def.EnumType(taskType) {
	case models.TaskTypeRNASerial:
		price = RNALevelToPriceMap[taskConf.Level]
		consoleApiId = RNALevelToIdMap[taskConf.Level]
	case models.TaskTypeProtein:
		price = ProteinLevelToPriceMap[taskConf.Level]
		consoleApiId = ProteinLevelToIdMap[taskConf.Level]
	case models.TaskTypeProteinSingle:
		price = ProteinSingleLevelToPriceMap[taskConf.Level]
		consoleApiId = ProteinSingleLevelToIdMap[taskConf.Level]
	case models.TaskTypeAdmet:
		price = AdmetForecastPrice
		consoleApiId = AdmetForecast
		if taskData.IsAPI > 0 {
			consoleApiId = AdmetApi
		}
	case models.TaskTypeProteinComplex:
		price = ProteinComplexLevelToPriceMap[taskConf.Level]
		consoleApiId = ProteinComplexLevelToIdMap[taskConf.Level]
	case models.TaskTypeFold:
		price = LinearFoldPrice
		consoleApiId = LinearFold
	case models.TaskTypePartition:
		price = LinearPartitionPrice
		consoleApiId = LinearPartition
	case models.TaskTypeKYKT:
		price = KYKTPrice
		consoleApiId = KYKT
	case models.TaskType5UTR:
		price = UTRPrice
		consoleApiId = UTR
	}

	// 价格计算
	amount := price
	chargeNum := 1
	chargeType := models.ChargeTypeNum
	chargeDuration := taskData.ChargeEndTime.Unix() - taskData.ChargeStartTime.Unix()
	if price < 10 && def.EnumType(taskType) != models.TaskTypeAdmet {
		amount = helpers.FormatFloat(price*math.Ceil(float64(chargeDuration)/60), 2)
		chargeType = models.ChargeTypeDuration
	}
	// advance 特殊判断
	if def.EnumType(taskType) == models.TaskTypeRNASerial {
		chargeNum = int(taskConf.Number)
		amount = float64(chargeNum) * amount
	}
	// admet 判断
	if def.EnumType(taskType) == models.TaskTypeAdmet {
		chargeNum = int(taskConf.Number)
		amount = helpers.FormatFloat(price*float64(chargeNum), 2)
	}

	// 任务
	var couponIdList []int64
	if taskData.ChargeTab == def.EnumType(uint64(models.ChargeTabCoupon)) {
		couponM := models.Coupon{}
		couponList, err := couponM.GetListByTaskType(ctx, taskData.UserId, taskData.Type)
		if err != nil {
			helpers.LogError(ctx, err)
			go helpers.HelixNotice(ctx, "charge failed, task_id:"+strconv.Itoa(int(taskData.ID)))
		}

		// 消耗优惠券
		needAmount := amount
		for _, coupon := range couponList {
			couponIdList = append(couponIdList, int64(coupon.ID))
			if coupon.RestAmount > needAmount {
				coupon.RestAmount = helpers.FormatFloat(coupon.RestAmount-needAmount, 2)
				_ = coupon.Save(ctx)
				break
			}
			needAmount = amount - coupon.RestAmount
			coupon.RestAmount = 0.01
			coupon.Status = models.CouponStatusLose
			_ = coupon.Save(ctx)
		}
	}

	// do bill
	err := doBill(ctx, taskData, amount, int64(chargeNum), int64(chargeType), int64(consoleApiId), int64(taskConf.Level), couponIdList)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, "charge failed, task_id:"+strconv.Itoa(int(taskData.ID)))
		return false
	}

	return true
}

// 账单
func doBill(ctx context.Context, taskData models.Task, amount float64, chargeNum, chargeType, consoleId, level int64, couponIdList []int64) error {
	userM := models.User{}
	userInfo, _ := userM.GetUserById(ctx, taskData.UserId)
	if userInfo.ID < 0 {
		return errors.New("user info get failed")
	}
	if userInfo.NeedSwitch > 0 {
		userInfo.RealID = uint64(models.DefaultInnerRealID)
	}

	needPush := models.NeedPushFalse
	if taskData.ChargeTab == def.EnumType(models.ChargeTabBilling) {
		needPush = models.NeedPushTrue
	}
	couponIdListByte, _ := json.Marshal(couponIdList)
	billNew := models.Bill{
		UserId:         def.AutoIDType(userInfo.ID),
		RealId:         def.AutoIDType(userInfo.RealID),
		TaskId:         taskData.ID,
		TaskType:       taskData.Type,
		ChargeType:     def.EnumType(chargeType),
		ConsoleApiId:   consoleId,
		LogId:          def.AutoIDType(helpers.GetNumRandomStr(18)),
		StartTime:      taskData.ChargeStartTime,
		EndTime:        taskData.ChargeEndTime,
		ChargeNum:      chargeNum,
		ChargeDuration: taskData.ChargeEndTime.Unix() - taskData.ChargeStartTime.Unix(),
		CostAmount:     amount,
		CouponIdList:   string(couponIdListByte),
		NeedPush:       needPush,
		Level:          level,
	}
	bill, err := billNew.Add(ctx, billNew)
	if err != nil {
		return err
	}

	// 推送账单
	if needPush == models.NeedPushTrue {
		pipeMsg := PipeMsg{
			Uuid:        strconv.Itoa(int(bill.LogId)),
			TaskId:      strconv.Itoa(int(bill.ID)),
			AccountId:   strconv.Itoa(int(bill.RealId)),
			AccountType: accountTypeMap[def.EnumType(int(userInfo.Source))],
			ApiId:       strconv.Itoa(int(bill.ConsoleApiId)),
			StartTime:   bill.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:     bill.EndTime.Format("2006-01-02 15:04:05"),
			UsageNumber: strconv.Itoa(int(bill.ChargeNum)),
			Duration:    strconv.Itoa(int(bill.ChargeDuration)),
		}
		err = PushBigpipe(ctx, pipeMsg, chargeType)
		if err != nil {
			return err
		}
	}
	return nil
}
