package services

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/models"
)

// 任务类型转换
const (
	TaskTypeFold          def.EnumType = 300
	TaskTypePartition     def.EnumType = 301
	TaskTypeRNASerial     def.EnumType = 310
	TaskTypeAdmet         def.EnumType = 320
	TaskTypeMolActivity   def.EnumType = 330
	TaskTypeMolFormation  def.EnumType = 340
	TaskTypeProtein       def.EnumType = 350
	TaskTypeVirtualFilter def.EnumType = 360
	TaskTypeDoubleDrug    def.EnumType = 370
	TaskTypeAntibody      def.EnumType = 380 // 抗原
	TaskTypeNewCrown      def.EnumType = 381 // 新冠
)

var TaskTypeMap = map[def.EnumType]def.EnumType{
	models.TaskTypeFold:          TaskTypeFold,
	models.TaskTypePartition:     TaskTypePartition,
	models.TaskTypeRNASerial:     TaskTypeRNASerial,
	models.TaskTypeAdmet:         TaskTypeAdmet,
	models.TaskTypeSelfAdmet:     TaskTypeAdmet,
	models.TaskTypeMolActivity:   TaskTypeMolActivity,
	models.TaskTypeMolFormation:  TaskTypeMolFormation,
	models.TaskTypeProtein:       TaskTypeProtein,
	models.TaskTypeVirtualFilter: TaskTypeVirtualFilter,
	models.TaskTypeDoubleDrug:    TaskTypeDoubleDrug,
	models.TaskTypeAntibody:      TaskTypeAntibody,
	models.TaskTypeNewCrown:      TaskTypeNewCrown,
}

// 运行方式
const (
	RunModeTrain = "train"
	RunModeInfer = "infer"
)

var RunModeMap = map[def.EnumType]string{
	models.FuncTypeTrain:         RunModeTrain,
	models.FuncTypeTrainClassify: RunModeTrain,
	models.FuncTypeForecast:      RunModeInfer,
}

// 基于方式
const (
	BasedTypeTarget = "target"
)

var BasedTypeMap = map[def.EnumType]string{
	models.TaskTypeMolFormation: BasedTypeTarget,
}

// 训练类型
const (
	TrainTypeClassify   = "Classification"
	TrainTypeRegression = "Regression"
)

var TrainTypeMap = map[def.EnumType]string{
	models.FuncTypeTrainClassify: TrainTypeClassify,
	models.FuncTypeTrain:         TrainTypeRegression,
}

// Server 数据响应体
const RetCodeSucc = "RET_OK"

type ServerResp struct {
	RetCode string         `json:"ret_code"`
	Results []ServerResult `json:"results"`
}
type ServerResult struct {
	OriTaskId string         `json:"ori_task_id"`
	TaskId    def.AutoIDType `json:"task_id"`
}

// 调度 mq 消息结构体
type MqResp struct {
	TaskId          def.AutoIDType    `json:"task_id"`
	RudderTaskId    def.AutoIDType    `json:"rudder_task_id"`
	FileUrl         string            `json:"file_url"`
	TrainStatus     def.EnumType      `json:"status"`
	ErrorCode       def.EnumType      `json:"error_code"`
	LogPath         string            `json:"log_path"`
	DownloadUrl     string            `json:"download_url"`
	ChargeStartTime def.TimestampType `json:"charge_start_time"`
	ChargeEndTime   def.TimestampType `json:"charge_end_time"`
	FinishTime      def.TimestampType `json:"finish_time"`
	Resource        string            `json:"resource_type"`
}

// SubmitTask 提交任务
func SubmitTask(ctx context.Context, serial models.Serial) (ServerResult, error) {
	var resp ServerResult

	// dataset
	dataset := make(map[string]any)
	if len(serial.Serial) == 0 {
		dataset["type"] = "file"
		dataset["file_url"] = serial.FileUrl
	} else {
		dataset["type"] = "string"
		dataset["smiles"] = serial.Serial
		if serial.Type == models.TaskTypeMolActivity {
			dataset["protein"] = serial.Protein
		}
	}

	// config
	config := map[string]any{"dataset_name": serial.Name}
	if serial.Status == models.StatusOnline {
		config["cache_operate"] = "publish"
	} else {
		config["cache_operate"] = "withdraw"
	}
	if serial.Type == models.TaskTypeSelfAdmet {
		config["train_type"] = TrainTypeMap[serial.FuncType]
	}
	if serial.Type == models.TaskTypeMolFormation {
		config["based_type"] = BasedTypeMap[serial.Type]
	}
	if serial.Type == models.TaskTypeMolActivity && serial.FuncType == models.FuncTypeForecast {
		config["use_bingdingdb"] = true
		config["use_chembl"] = true
	}
	configByte, _ := json.Marshal(config)

	// 获取bos配置
	bosOutputPath, err := helpers.GetConfField(env.ConfDir()+"/bos.toml", "ServerOutputPath")
	if err != nil {
		return resp, err
	}

	taskIdStr := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	item := map[string]any{
		"task_id":     "cache_" + taskIdStr,
		"task_type":   TaskTypeMap[serial.Type],
		"run_mode":    RunModeMap[serial.FuncType],
		"dataset":     dataset,
		"output_path": bosOutputPath + taskIdStr,
		"config":      string(configByte),
	}
	message := map[string]any{
		"items": []any{item},
	}

	// 提交任务
	serResp, err := callSched(ctx, message, "/AIPipelineService/paddlehelix_job_task_submit")
	if err != nil {
		return resp, err
	}
	if len(serResp.Results) == 0 {
		return resp, errors.New("result data null")
	}

	return serResp.Results[0], nil
}

// 调用调度
func callSched(ctx context.Context, message map[string]any, reqPath string) (ServerResp, error) {
	// 写日志
	messageJson, _ := json.Marshal(message)
	resource.LoggerService.Notice(ctx, string(messageJson))
	go helpers.HelixNotice(ctx, string(messageJson))

	ralReq := &ghttp.RalPostRequest{
		PostData: message,
		Encoder:  codec.JSONEncoder,
	}
	ralReq.Path = reqPath
	ralReq.Header = map[string][]string{
		"Log-Id":       {},
		"Content-Type": {"application/json;charset=UTF-8"},
	}

	var resp ServerResp
	ralResp := &ghttp.RalResponse{
		Data:    &ServerResp{RetCode: "Failed"},
		Decoder: codec.JSONDecoder,
	}
	err := ral.RAL(ctx, "scheduler", ralReq, ralResp)
	if err != nil {
		return resp, err
	}

	// 写日志
	respJson, _ := json.Marshal(ralResp.Data)
	resource.LoggerService.Notice(ctx, string(respJson))

	// 返回数据处理
	respData := ralResp.Data.(*ServerResp)
	if respData.RetCode != RetCodeSucc {
		return resp, errors.New("ret_code:" + respData.RetCode)
	}

	return *respData, nil
}
