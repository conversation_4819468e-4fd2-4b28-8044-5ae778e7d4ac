package services

import (
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/models"
)

const (
	AccountPassType = "passport"
	AccountUCType   = "uc"

	HelixFold1       = 1174
	HelixFold2       = 1175
	HelixFold3       = 1176
	HelixFoldSingle1 = 1177
	HelixFoldSingle2 = 1178
	HelixFoldSingle3 = 1179
	LinearDesign1    = 1180
	LinearDesign2    = 1181
	LinearDesign3    = 1182
	AdmetForecast    = 1264
	AdmetApi         = 1265
	ProteinComplex1  = 1262
	ProteinComplex2  = 1263
	KYKT             = 1437
	UTR              = 1438
	LinearFold       = 1439
	LinearPartition  = 1440
)

var PackageApiList = []int{
	LinearDesign1,
	LinearDesign2,
	LinearDesign3,
	AdmetForecast,
	AdmetApi,
}

var AllApiList = []int{
	HelixFold1,
	HelixFold2,
	HelixFold3,
	He<PERSON>FoldSingle1,
	HelixFoldSingle2,
	HelixFoldSingle3,
	LinearDesign1,
	LinearDesign2,
	LinearDesign3,
	AdmetForecast,
	AdmetApi,
	ProteinComplex1,
	ProteinComplex2,
	KYKT,
	UTR,
	LinearFold,
	LinearPartition,
}

var accountTypeMap = map[def.EnumType]string{
	models.SourcePassport: AccountPassType,
	models.SourceUC:       AccountUCType,
	models.SourceGitHub:   AccountPassType,
}

// FundResp 获取用量信息
type FundResp struct {
	Success bool              `json:"success"`
	Code    def.IntType       `json:"code"`
	Result  AccountFundResult `json:"result"`
}
type AccountFundResult struct {
	Cash        float64 `json:"cash"`
	Rebate      float64 `json:"rebate"`
	TotalAmount float64 `json:"totalAmount"`
}

func GetAccountFund(ctx context.Context, accountId uint64, source uint64) (AccountFundResult, error) {
	message := map[string]any{
		"accountId":   accountId,
		"accountType": accountTypeMap[def.EnumType(source)],
	}

	resp := &FundResp{}
	err := callService(ctx, message, "console", "/console/easydl/inner/accountfund", resp)
	if err != nil {
		return AccountFundResult{}, err
	}

	// 返回数据处理
	if !resp.Success {
		return AccountFundResult{}, errors.New("get account info failed")
	}
	return resp.Result, nil
}

// call
func callService(ctx context.Context, message any, serviceName, reqPath string, resp any) error {
	messageJson, _ := json.Marshal(message)
	helpers.LogNotice(ctx, string(messageJson))
	go helpers.HelixNotice(ctx, string(messageJson)+"--uri:"+reqPath)

	ralReq := &ghttp.RalPostRequest{
		PostData: message,
		Encoder:  codec.JSONEncoder,
	}
	ralReq.Path = reqPath
	ralReq.Header = map[string][]string{
		"Log-Id":       {},
		"Content-Type": {"application/json;charset=UTF-8"},
	}

	ralResp := &ghttp.RalResponse{
		Data:    &resp,
		Decoder: codec.JSONDecoder,
	}
	err := ral.RAL(ctx, serviceName, ralReq, ralResp)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error()+"---serviceName: "+serviceName)
		return err
	}

	// 写数据日志
	respJson, _ := json.Marshal(ralResp.Data)
	helpers.LogNotice(ctx, string(respJson))
	go helpers.HelixNotice(ctx, string(respJson))

	return nil
}
