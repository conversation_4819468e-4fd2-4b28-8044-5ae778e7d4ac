package jobs

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/helix_web/models"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/services"
)

// SyncTrial check trial user
func SyncTrial(ctx context.Context) {
	ctx1 := logit.NewContext(ctx)
	defer helpers.DeferFunc(ctx1)()

	for {
		if time.Now().Hour() == 0 {
			err := services.CheckTrial(ctx1)
			if err != nil {
				helpers.LogError(ctx1, err)

				// 发送Hi
				go helpers.HelixNotice(ctx1, "--- check trial error timeout pause ---"+err.Error())
			}
		}
		if time.Now().Hour() == 1 {
			err := services.CheckCoupon(ctx1)
			if err != nil {
				helpers.LogError(ctx1, err)

				// 发送Hi
				go helpers.HelixNotice(ctx1, "--- check coupon error timeout pause ---"+err.Error())
			}
		}

		// 睡眠 1200 秒
		resource.LoggerService.Notice(ctx1, "------- check trial and coupon sleep 1200s -------")
		time.Sleep(time.Second * 1200)
	}
}

func SyncBillingOrders(ctx context.Context) {
	now := time.Now()
	next := now.Add(30 * time.Minute)
	timer := time.NewTimer(next.Sub(now))
	for {
		select {
		case <-timer.C:
			SyncBillingOrdersInternal(ctx)
			now = time.Now()
			next = now.Add(30 * time.Minute)
			timer.Reset(next.Sub(now))
		case <-ctx.Done():
			return
		}
	}
}

func SyncBillingOrdersInternal(ctx context.Context) {
	fmt.Println("======== start to sync billing orders ========")
	// 查询最近两天的所有任务
	now := time.Now().Unix()
	taskM := models.Task{}
	cond := models.TaskCond{
		GtCreatedAt: helpers.UnixToTimeStr(now - 24*3600),
		LtCreatedAt: helpers.UnixToTimeStr(now),
		IsAPI:       -1,
	}
	// 分页处理任务数据
	pageNum, pageSize := 1, 100
	for {
		tasks, err := taskM.GetByCond(ctx, cond, pageSize, pageNum, "created_at")
		if len(tasks) <= 0 {
			fmt.Println("======== finish sync billing orders ========")
			return
		}
		if err != nil {
			helpers.HelixNotice(ctx, "--- sync billing orders db error ---"+err.Error())
			continue
		}
		for _, task := range tasks {
			if task.Status != int32(models.TaskStatusSucc) || task.Balance <= 0 {
				continue
			}
			userM := models.User{}
			user, err := userM.GetUserById(ctx, task.UserId)
			if err != nil {
				helpers.HelixNotice(ctx, "--- sync billing orders db error ---"+err.Error())
				continue
			}
			billM := models.Bill{}
			billN, err := billM.GetByTaskID(ctx, task.ID)
			if err != nil {
				helpers.HelixNotice(ctx, "--- sync billing orders db error ---"+err.Error())
				continue
			}
			if billN.ID <= 0 {
				var chargeDuration time.Duration
				if !task.ChargeStartTime.IsZero() && !task.ChargeEndTime.IsZero() {
					chargeDuration = task.ChargeEndTime.Sub(task.ChargeStartTime)
				}
				billN = &models.Bill{
					UserId:         def.AutoIDType(user.ID),
					RealId:         def.AutoIDType(user.RealID),
					TaskId:         task.ID,
					TaskType:       task.Type,
					ChargeType:     10,
					ConsoleApiId:   1,
					LogId:          1,
					StartTime:      task.ChargeStartTime,
					EndTime:        task.ChargeEndTime,
					ChargeNum:      1,
					ChargeDuration: int64(chargeDuration.Seconds()),
					CostAmount:     task.Balance,
					CouponIdList:   task.Coupons,
					NeedPush:       1,
					Level:          1,
					CreatedAt:      task.CreatedAt,
				}
				_, err = billM.Add(ctx, *billN)
				if err != nil {
					helpers.HelixNotice(ctx, "--- sync billing orders db error ---"+err.Error())
				}
				fmt.Println(fmt.Sprintf("taskID: %d, orderID: {%s} sync success", task.ID, task.OrderID))
			} else {
				fmt.Println(fmt.Sprintf("taskID: %d, orderID: {%s} already sync, skip", task.ID, task.OrderID))
			}
		}
		pageNum += 1
	}
}
