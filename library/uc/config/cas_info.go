package config

type CasInfo struct {
	Servers      []string
	CookieDomain string
	CookiePath   string
	CookieStKey  string
	CookieIDKey  string
	CookieRnKey  string
	Appid        int
	StKeyName    string
	AppKey       string
	Encoding     string
	TimeoutMs    int
	AutoRedirect bool
	LoginURL     string
	JumpURL      string
	UsingSSL     bool
}

// NewCasInfo factory mathod for create default config
func NewCasInfo() *CasInfo {
	return &CasInfo{
		Servers:      make([]string, 0, 50),
		CookieDomain: ".baidu.com",
		CookiePath:   "/",
		<PERSON><PERSON><PERSON>t<PERSON><PERSON>:  "__cas__st__",
		<PERSON><PERSON><PERSON><PERSON><PERSON>:  "__cas__id__",
		<PERSON>ieRn<PERSON>ey:  "__cas__rd__",
		StKeyName:    "castk",
		Encoding:     "utf-8",
		TimeoutMs:    1000,
		AutoRedirect: false,
		LoginURL:     "https://cas.baidu.com/?tpl=www2",
		JumpURL:      "https://cas.baidu.com/?action=check&appid=",
		UsingSSL:     false,
	}
}

// AddServer ...
func (config *CasInfo) AddServer(server string) {
	newServer := append(config.Servers, server)
	config.Servers = newServer
}
