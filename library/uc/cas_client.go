package uc

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"icode.baidu.com/helix_web/library/uc/config"
	"icode.baidu.com/helix_web/library/uc/protocol"
)

type CasClient struct {
	reqURL   string
	request  *http.Request
	writer   *http.ResponseWriter
	config   *config.CasInfo
	executor *protocol.Executor
}

// ClientData 浏览器、客户端可以存储的数据
type ClientData struct {
	Ucid int
	St   string
}

func (cd *ClientData) check() bool {
	if cd.Ucid > 0 && len(cd.St) >= 4 {
		return true
	}
	return false
}

// NewCasClient ...
func NewCasClient(r *http.Request, w *http.ResponseWriter, info *config.CasInfo) *CasClient {
	var comm *protocol.Executor
	comm = protocol.NewCommunicator(info.Servers)
	prefix := "http://"
	if info.UsingSSL {
		prefix = "https://"
	}
	url := prefix + r.Host + r.URL.RequestURI()
	return &CasClient{
		reqURL:   url,
		request:  r,
		writer:   w,
		config:   info,
		executor: comm,
	}
}

// ValiateSt ...
func (client *CasClient) ValiateSt() *protocol.ValidateResult {
	clientData := new(ClientData)
	//是否首次跳入: 如果URL中存在临时票据，且临时票据成功交换到长期票据，则认为是首次跳入
	strStKey := client.request.URL.Query().Get(client.config.StKeyName)
	needSetCookie := false
	if len(strStKey) <= 64 && len(strStKey) > 20 {
		//只要临时票据满足简单的长度规范，就认为合法，并向服务器发起交换请求
		//请求成功则更新ST和UCID信息，失败则不做任何事情，继续检查Cookie中是否已经存在ST
		unValidUcid, unValidSt, err := client.exchangeSt(strStKey)
		if err == nil && unValidUcid > 0 && len(unValidSt) >= 4 {
			clientData.Ucid = unValidUcid
			clientData.St = unValidSt
			needSetCookie = true
		}
	}

	if !clientData.check() {
		stCookie, errSt := client.request.Cookie(client.config.CookieStKey)
		ucidCookie, errUcid := client.request.Cookie(client.config.CookieIDKey)
		if errSt == nil && errUcid == nil {
			clientData.Ucid, _ = strconv.Atoi(ucidCookie.Value)
			clientData.St = stCookie.Value
			//依然没取到正确的ucid和st，直接返回错误
			if !clientData.check() {
				//失败，清除 cookie
				client.clearCasCookies()

				//Java版的心跳检测主要用于缓存，暂不予实现
				//如果开启，执行自动重定向(一般是登录页)
				if client.config.AutoRedirect {
					client.gotoJumpURL()
				}
			}
		}
	}

	vResp, err := client.DoValidate(clientData)
	//长期票据(ST)验证成功，直接返回
	if err == nil && vResp.Error == nil {
		if needSetCookie {
			client.setCasCookies(clientData.St, clientData.Ucid)
		}
		return vResp.Result
	}

	//失败，清除 cookie
	client.clearCasCookies()

	//Java版的心跳检测意义不大，不予实现
	//如果开启，执行自动重定向(一般是登录页)
	if client.config.AutoRedirect {
		client.gotoJumpURL()
	}

	return nil
}

func (client *CasClient) gotoJumpURL() {
	client.gotoJumpURLWithParams("")
}

func (client *CasClient) gotoJumpURLWithParams(sMoreURLParam string) {
	sRb := ""
	if !client.config.AutoRedirect {
		sRb += "&rb=1"
	}
	if len(sMoreURLParam) > 0 && !strings.HasPrefix(sMoreURLParam, "&") {
		sMoreURLParam = "&" + sMoreURLParam
	}

	newURL := client.config.JumpURL + strconv.Itoa(client.config.Appid) + sRb + sMoreURLParam

	seperator := "?"
	if strings.Index(newURL, seperator) > 8 {
		seperator = "&"
	}

	newURL = newURL + seperator + "u=" + url.PathEscape(client.reqURL)
	fmt.Print(newURL)
	http.Redirect(*client.writer, client.request, newURL, http.StatusFound)
}

func (client *CasClient) setCookie(st, ucid, rn string, w http.ResponseWriter) {
	stCookie := http.Cookie{
		Name:     client.config.CookieStKey,
		Value:    st,
		Path:     client.config.CookiePath,
		Domain:   client.config.CookieDomain,
		MaxAge:   -1,
		HttpOnly: true,
	}
	http.SetCookie(w, &stCookie)
	idCookie := http.Cookie{
		Name:     client.config.CookieIDKey,
		Value:    ucid,
		Path:     client.config.CookiePath,
		Domain:   client.config.CookieDomain,
		MaxAge:   -1,
		HttpOnly: true,
	}
	http.SetCookie(w, &idCookie)
	rnCookie := http.Cookie{
		Name:     client.config.CookieRnKey,
		Value:    rn,
		Path:     client.config.CookiePath,
		Domain:   client.config.CookieDomain,
		MaxAge:   -1,
		HttpOnly: true,
	}
	http.SetCookie(w, &rnCookie)
}

func (client *CasClient) exchangeSt(strStKey string) (ucid int, st string, err error) {
	// 如果URL中包含临时票据，优先尝试拿临时票据换取正式票据
	if strStKey != "" && len(strStKey) > 20 && len(strStKey) <= 64 {
		// getRealSt here
		exchangeStService := protocol.NewExchangeStService(client.executor)

		binReq := exchangeStService.EncodeRequest(client.config.Appid, strStKey, client.config.AppKey)
		binResp, err := exchangeStService.Executor.Execute(binReq)
		if err != nil {
			log.Println("err execute exchangeSt command" + err.Error())
			return -1, "", err
		}
		exchangeResponse, err := exchangeStService.DecodeResponse(binResp)
		if err != nil {
			log.Println("err decoding exchangeSt response:" + err.Error())
			return -1, "", err
		}
		if exchangeResponse.Error != nil {
			log.Println("err decoding exchangeSt response:" + exchangeResponse.Error.Message)
			return -1, "", errors.New("error exchange st")
		}
		st = exchangeResponse.Result.St
		ucid = exchangeResponse.Result.Ucid
		return ucid, st, nil
	}
	return -1, "", errors.New("invalid format of stkey: " + strStKey)
}

// DoValidate ...
func (client *CasClient) DoValidate(data *ClientData) (*protocol.ValidateResp, error) {
	validator := protocol.NewValidatorService(client.executor)
	binReq := validator.EncodeRequest(data.Ucid, client.config.Appid, data.St, client.config.AppKey)
	binRes, _ := validator.Executor.Execute(binReq)

	fmt.Println(string(binRes))

	response := validator.DecodeResponse(binRes)

	fmt.Println(response.Error)
	fmt.Println(response.Result)
	return response, nil
}

func (client *CasClient) clearCasCookies() {
}

func (client *CasClient) setCasCookies(st string, ucid int) {
	http.SetCookie(*client.writer, &http.Cookie{
		Name:   client.config.CookieStKey,
		Value:  st,
		MaxAge: 86400,
		Path:   client.config.CookiePath,
		Domain: client.config.CookieDomain,
	})
	http.SetCookie(*client.writer, &http.Cookie{
		Name:   client.config.CookieIDKey,
		Value:  strconv.Itoa(ucid),
		MaxAge: 86400,
		Path:   client.config.CookiePath,
		Domain: client.config.CookieDomain,
	})
}
