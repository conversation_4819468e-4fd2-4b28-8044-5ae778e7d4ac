package protocol

import "encoding/json"

// NewValidatorService ...
func NewValidatorService(executor *Executor) *ValidateService {
	return &ValidateService{
		Executor: executor,
	}
}

type ValidateService struct {
	Executor *Executor
}

// EncodeRequest ...
func (service *ValidateService) EncodeRequest(ucid, appid int, st, key string) []byte {
	body := service.Executor.NewRequest("validate")
	params := struct {
		Ucid   int    `json:"ucid"`
		Appid  int    `json:"appid"`
		St     string `json:"st"`
		StLen  int    `json:"st_len"`
		Key    string `json:"key"`
		KeyLen int    `json:"key_len"`
	}{
		Ucid:   ucid,
		Appid:  appid,
		St:     st,
		StLen:  len(st),
		Key:    key,
		KeyLen: len(key),
	}

	body.Params[0] = params

	jsonBytes, error := json.Marshal(body)
	if jsonBytes == nil {
		panic(error)
	}

	return jsonBytes
}

// DecodeResponse ...
func (service *ValidateService) DecodeResponse(rawResp []byte) *ValidateResp {
	response := new(ValidateResp)
	error := json.Unmarshal(rawResp, response)

	if error == nil {
		//do someting exception
	}

	return response
}

// ValidateResp validate api request params
type ValidateResp struct {
	Response
	Result *ValidateResult `json:"result"`
}

// ValidateResult validate api response result
type ValidateResult struct {
	Ucid           int    `json:"ucid"`
	Tag            int    `json:"tag"`
	Utype          int    `json:"utype"`
	LoginAppid     int    `json:"login_appid"`
	RegAppid       int    `json:"reg_appid"`
	ExpireDays     int    `json:"expire_days"`
	LoginTime      int    `json:"login_time"`
	Usename        string `json:"username"`
	Ucname         string `json:"ucname"`
	Tgc            string `json:"tgc"`
	TgcLen         int    `json:"tgc_len"`
	KickoutFlag    int    `json:"kickout_flag"`
	SessionTimeout int    `json:"session_timeout"`
}
