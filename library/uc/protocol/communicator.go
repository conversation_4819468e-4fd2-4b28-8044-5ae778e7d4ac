package protocol

import (
	"fmt"
	"io/ioutil"
	"math/rand"
	"net/http"
	"strings"
	"time"
)

type Communicator interface {
	Execute(req []byte) ([]byte, error)
}

type Executor struct {
	servers []string
}

func NewCommunicator(servers []string) *Executor {
	return &Executor{
		servers: servers,
	}
}

type Response struct {
	ID     int64        `json:"id"`
	Result *interface{} `json:"result"`
	Error  *RespError   `json:"error"`
}

type RespError struct {
	Code    int    `json:"code"`
	CasCode int    `json:"cas_code"`
	Message string `json:"message"`
}

type Request struct {
	Jsonrpc string        `json:"jsonrpc"`
	Method  string        `json:"method"`
	Params  []interface{} `json:"params"`
	ID      uint64        `json:"id"`
}

// Execute ...
func (ex *Executor) Execute(req []byte) ([]byte, error) {
	rand.Seed(time.Now().UnixNano())
	idx := rand.Int31n(int32(len(ex.servers)))
	selected := ex.servers[rand.Int31n(int32(len(ex.servers)))]
	fmt.Println(selected, idx, ex.servers, ex.servers)
	content := "application/baidu.json-rpc;charset=utf-8"
	resp, err := http.Post(selected, content, strings.NewReader(string(req)))

	if resp == nil {
		fmt.Println(err)
		return nil, err
	}

	result, err := ioutil.ReadAll(resp.Body)
	if result == nil {
		fmt.Println(err)
		return nil, err
	}

	return result, nil
}

// NewRequest ...
func (ex *Executor) NewRequest(method string) *Request {
	rand.Seed(time.Now().UnixNano())
	return &Request{
		Jsonrpc: "2.0",
		ID:      rand.Uint64(),
		Method:  method,
		Params:  make([]interface{}, 1),
	}
}

// NewResponse ...
func (ex *Executor) NewResponse(method string) *Response {
	return &Response{}
}
