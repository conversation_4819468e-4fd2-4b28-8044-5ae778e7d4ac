package protocol

import "encoding/json"

type ExchangeStService struct {
	Executor *Executor
}

func NewExchangeStService(executor *Executor) *ExchangeStService {
	return &ExchangeStService{executor}
}

func (service *ExchangeStService) EncodeRequest(appid int, stKey, key string) []byte {
	body := service.Executor.NewRequest("exchange_st")
	params := struct {
		Appid    int    `json:"appid"`
		StKey    string `json:"st_key"`
		StKeyLen int    `json:"st_key_len"`
		Key      string `json:"key"`
		KeyLen   int    `json:"ken_len"`
	}{
		Appid:    appid,
		StKey:    stKey,
		StKeyLen: len(stKey),
		Key:      key,
		KeyLen:   len(key),
	}

	body.Params[0] = params

	jsonBytes, error := json.Marshal(body)
	if jsonBytes == nil {
		panic(error)
	}

	return jsonBytes
}

// DecodeResponse ...
func (service *ExchangeStService) DecodeResponse(rawResp []byte) (resp *ExchangeStResp, err error) {
	response := new(ExchangeStResp)
	error := json.Unmarshal(rawResp, response)

	if error != nil {
		return nil, error
	}

	return response, nil
}

// ExchangeStResp validate api request params
type ExchangeStResp struct {
	Response
	Result *ExchangeStResult `json:"result"`
}

// ExchangeStResult validate api response result
type ExchangeStResult struct {
	Ucid  int    `json:"ucid"`
	St    string `json:"st"`
	StLen int    `json:"st_len"`
}
