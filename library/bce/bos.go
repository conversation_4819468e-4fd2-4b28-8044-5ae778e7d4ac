package bce

import (
	"io/ioutil"
	"strings"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/sts"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/library/def"
)

const (
	ObjectUrlExpireTime = 300 // 300s

	EndpointBJ  = "bj.bcebos.com"
	EndpointSZ  = "su.bcebos.com"
	CdnEndpoint = "cdn.bcebos.com"

	bosAK = "50c8bb753dcb4e1d8646bb1ffefd3503"
	bosSK = "2ed937b2bf3d473f9d60ae80721a15d1"

	devDefaultBucket    = "bml-test-test"
	onlineDefaultBucket = "easydl-download"
)

var defaultBucketMap = map[string]string{
	env.RunModeDebug:   devDefaultBucket,
	env.RunModeTest:    devDefaultBucket,
	env.RunModeRelease: devDefaultBucket,
}
var endpointMap = map[string]string{
	"bj": EndpointBJ,
	"su": EndpointSZ,
}

// 获取上传 token
func GetSessionToken() (map[string]any, error) {
	var resp map[string]any

	// 创建STS服务的Client对象，Endpoint使用默认值
	stsClient, err := sts.NewClient(bosAK, bosSK)
	if err != nil {
		return resp, err
	}

	// 获取临时认证token，有效期为60秒，ACL为空
	stsResult, err := stsClient.GetSessionToken(120, "")
	if err != nil {
		return resp, err
	}

	resp = map[string]any{
		"temp_ak":       stsResult.AccessKeyId,
		"temp_sk":       stsResult.SecretAccessKey,
		"session_token": stsResult.SessionToken,
		"endpoint":      EndpointBJ,
		"bucket":        defaultBucketMap[env.RunMode()],
	}
	return resp, nil
}

// 获取文件内容
func GetObject(bucket, object string) (string, error) {
	// 处理bucket
	if len(bucket) == 0 {
		bucket = defaultBucketMap[env.RunMode()]
	}

	bosClient, err := bos.NewClient(bosAK, bosSK, getEndpointByBucket(bucket))
	if err != nil {
		return "", err
	}

	objectRes, err := bosClient.BasicGetObject(bucket, object)
	if err != nil {
		return "", err
	}

	bodyData, err := ioutil.ReadAll(objectRes.Body)
	if err != nil {
		return "", err
	}
	defer objectRes.Body.Close()

	return string(bodyData), nil
}

// 获取访问链接
func GenerateObjectUrl(bucket, object string, expireInSeconds def.IntType) (string, error) {
	bosClient, err := bos.NewClient(bosAK, bosSK, getEndpointByBucket(bucket))
	if err != nil {
		return "", err
	}

	objectUrl := bosClient.BasicGeneratePresignedUrl(bucket, object, expireInSeconds)
	objectUrl = strings.Replace(objectUrl, "http:", "https:", 1)

	return objectUrl, nil
}

func getEndpointByBucket(bucket string) string {
	bosClient, err := bos.NewClient(bosAK, bosSK, EndpointBJ)
	if err != nil {
		return EndpointBJ
	}

	location, err := bosClient.GetBucketLocation(bucket)
	if err != nil || (location != "bj" && location != "su") {
		return EndpointBJ
	}

	return endpointMap[location]
}
