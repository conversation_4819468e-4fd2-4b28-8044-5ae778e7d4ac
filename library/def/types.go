package def

////// base type

type TimestampType = int64
type CommonType = uint32
type CountType = int64
type AutoIDType = int64   // db自增id
type AutoID32Type = int32 // db自增id
type EnumType = int32
type ErrCodeType = int32
type IntType = int

////// other

type Integer interface {
	int | uint | int8 | uint8 | uint16 | int16 | uint32 | int32 | uint64 | int64
}

type IntegerString interface {
	Integer | string
}

type TrimString string
