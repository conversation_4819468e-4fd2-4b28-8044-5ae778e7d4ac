package chpc

import (
	"context"
	"errors"
	"fmt"
	"time"

	bceauth "github.com/baidubce/bce-sdk-go/auth"
	bcehttp "github.com/baidubce/bce-sdk-go/http"

	"icode.baidu.com/baidu/bce-iam/sdk-go/auth"
	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
)

const (
	ObjectURLExpireTime = 300 // 300s
)

var (
	iamClient *iam.BceClient
	iamCofig  *iam.BceClientConfiguration
	stsConfig *iam.StsClientConfiguration
)

type StsConfig struct {
	Endpoint string
	Version  string
}

func init() {
	err := conf.Parse(env.ConfDir()+"/iam.toml", &iamCofig)
	if err != nil {
		panic(err)
	}
	iamCofig.Retry = &iam.NoRetryPolicy{}
	iamClient = iam.NewBceClient(iamCofig)

	err = conf.Parse(env.ConfDir()+"/sts.toml", &stsConfig)
	if err != nil {
		panic(err)
	}
}

// newStsClient 创建sts客户端
func newStsClient() *iam.StsBceClient {
	serviceToken, err := iamClient.GetConsoleToken(false, "")
	if err != nil {
		panic(fmt.Sprintf("Failed to GetConsoleToken: %+v", err))
	}
	serviceAccessKeys, err := iamClient.GetAccessKeys(serviceToken.User.ID, false)
	if len(serviceAccessKeys.AccessKeys) == 0 {
		panic(fmt.Sprintf("Failed to GetAccessKeys by service account: %+v", err))
	}
	StsConfig := &iam.StsClientConfiguration{
		Endpoint: stsConfig.Endpoint,
		Version:  stsConfig.Version,
		Credentials: &auth.BceCredentials{
			AccessKeyId:     serviceAccessKeys.AccessKeys[0].Access,
			SecretAccessKey: serviceAccessKeys.AccessKeys[0].Secret,
		},
		SignOption: &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			Timestamp:     time.Now().UTC().Unix(),
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true, // 必须设置为true
		},
		Retry: &iam.NoRetryPolicy{},
	}
	return iam.NewStsBceClient(StsConfig)
}

// ParseCredentialFromUserDomainID STS modify, 获取临时凭证
func ParseCredentialFromUserDomainID(iamUserDomainID string) *iam.Credential {
	assumeRoleArgs := iam.AssumeRoleArgs{
		DurationSeconds: 7200,
		AccountID:       iamUserDomainID,
		RoleName:        "BceServiceRole_console_chpc",
	}
	stsClient := newStsClient()
	credential, err := stsClient.AssumeRole(assumeRoleArgs)
	if err != nil {
		fmt.Println("Failed to AssumeRole:", err)
		return nil
	}
	return credential
}

// ParseCredentialFromUserID STS modify, 获取临时凭证，子用户使用
func ParseCredentialFromUserID(iamUserDomainID string, iamUserID string) (*iam.Credential, error) {
	assumeRoleArgs := iam.AssumeRoleArgs{
		DurationSeconds: 7200,
		AccountID:       iamUserDomainID,
		RoleName:        "BceServiceRole_console_chpc",
		UserID:          iamUserID,
	}
	stsClient := newStsClient()
	credential, err := stsClient.AssumeRole(assumeRoleArgs)
	if err != nil {
		fmt.Println("Failed to AssumeRole:", err)
		return nil, err
	}
	return credential, nil
}

// IsOpenCHPCService 判断是否开启了CHPC服务，返回bool类型的值，true表示已经开启，false表示未开启
// 参数：
//
//	iamUserDomainID string - IAM用户所在的用户域ID，格式为"<user_id>-<domain_id>"
//
// 返回值：
//
//	bool - 如果IAM用户所在的用户域ID包含有效的且非空的AK/SK信息，则返回true；否则返回false
func IsOpenCHPCService(iamUserDomainID string) bool {
	if credential := ParseCredentialFromUserDomainID(iamUserDomainID); credential == nil {
		return false
	}
	return true
}

// GenerateStsAuthHeader STS modify, 传入临时凭证并签名
func GenerateStsAuthHeader(req *bcehttp.Request, ak, sk string) {
	cred, _ := bceauth.NewBceCredentials(ak, sk)
	opts := &bceauth.SignOptions{
		HeadersToSign: map[string]struct{}{
			"host": {},
		},
		ExpireSeconds: ObjectURLExpireTime,
	}
	signer := bceauth.BceV1Signer{}
	signer.Sign(req, cred, opts)
}

// AssembleStsRequest 将请求信息组装成STS请求，并设置相应的头部和签名信息
// ctx: 上下文对象，可以为nil
// req: *bcehttp.Request类型，需要组装的请求对象
// accountID: string类型，账户ID，用于解析用户域ID获取STS凭证信息
// method: string类型，HTTP方法，如"GET"、"POST"等
// endpoint: string类型，服务器地址，如"sts.bj.baidubce.com"
// uri: string类型，URI路径，如"/v1/session"
// 无返回值
func AssembleStsRequest(ctx context.Context, req *bcehttp.Request, accountID, userID, method, endpoint, uri string) error {
	if len(accountID) <= 0 {
		return errors.New("accountID is empty")
	}
	// 解析用户AK、SK
	cred, err := ParseCredentialFromUserID(accountID, userID)
	if err != nil {
		return err
	}
	clientConf := bcehttp.ClientConfig{}
	bcehttp.InitClient(clientConf)
	req.SetProtocol("http")
	req.SetMethod(method)
	req.SetHost(endpoint)
	req.SetUri(uri)
	req.SetHeader("Host", endpoint)
	req.SetHeader("Content-Type", "application/json")
	req.SetHeader("x-bce-security-token", cred.SessionToken)
	GenerateStsAuthHeader(req, cred.AccessKeyID, cred.SecretAccessKey)
	return nil
}
