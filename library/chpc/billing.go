package chpc

import (
	"context"
	"encoding/json"
	"errors"
	"io/ioutil"

	bcehttp "github.com/baidubce/bce-sdk-go/http"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/helpers"
)

var (
	billingConfig *BillingConfig
)

type BillingConfig struct {
	FinanceEndpoint  string
	CouponEndpoint   string
	URI              string
	FinanceStatusURI string
	CouponURI        string
	OrderEndpoint    string
	OrderURI         string
}

type QueryOrdersResp struct {
	Orders []*Order `json:"orders"`
}

type Order struct {
	UUID      string    `json:"uuid"`
	AccountID string    `json:"accountId"`
	UserID    string    `json:"userId"`
	Fee       *OrderFee `json:"fee"`
	Status    string    `json:"status"`
}

type OrderFee struct {
	Price          float64 `json:"price"`
	Cash           float64 `json:"cash"`
	Coupon         float64 `json:"coupon"`
	DiscountAmount float64 `json:"discountAmount"`
}

func init() {
	err := conf.Parse(env.ConfDir()+"/billing.toml", &billingConfig)
	if err != nil {
		panic(err)
	}
}

func QueryOrderByID(ctx context.Context, accountID, orderID string) (*Order, error) {
	if len(orderID) <= 0 {
		return nil, errors.New("order id is empty")
	}
	// assemble request
	req := &bcehttp.Request{}
	err := AssembleStsRequest(ctx, req, accountID, "GET", billingConfig.OrderEndpoint, billingConfig.OrderURI)
	if err != nil {
		return nil, err
	}
	// execute request
	resp, err := bcehttp.Execute(req)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return nil, err
	}
	defer resp.Body().Close()
	// parse response
	body, err := ioutil.ReadAll(resp.Body())
	if err != nil {
		helpers.LogError(ctx, err)
		return nil, err
	}
	var queryOrdersResp QueryOrdersResp
	_ = json.Unmarshal(body, &queryOrdersResp)
	orders := queryOrdersResp.Orders
	// return order
	for _, order := range orders {
		if order.UUID == orderID {
			return order, nil
		}
	}
	return nil, errors.New("order not found")
}
