package resource

import (
	"github.com/go-playground/validator/v10"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/mysql"
	"icode.baidu.com/baidu/gdp/redis"

	gormAdapter "icode.baidu.com/baidu/gdp/gorm_adapter"
)

// LoggerService 业务访问日志(access_log)：log/service/service.log
var LoggerService logit.Logger

// MySQLClient mysql client 单例
var MySQLClient mysql.Client
var RedisClient redis.Client
var Gorm *gormAdapter.GormDB

var Validator *validator.Validate
