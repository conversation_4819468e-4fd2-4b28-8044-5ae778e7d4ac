package redis

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	AdminTokenLimitTime = 43200

	AdminTokenPrefix = "admin_token_"
	AdminInfoPrefix  = "admin_info_"

	UserWhiteKey       = "user_white_key"
	UserWhiteSwitchKey = "user_white_switch_key"
)

// 获取缓存
func Get(ctx context.Context, key string) string {
	redisClient := resource.RedisClient

	resStr, err := redisClient.Get(ctx, key).Result()
	if err != nil && err != redis.Nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.<PERSON>rror())
		return ""
	}

	return resStr
}

// 写入缓存(expire 单位为 s)
func Set(ctx context.Context, key string, value any, expire def.IntType) bool {
	redisClient := resource.RedisClient

	expiration := time.Second * time.Duration(expire)
	_, err := redisClient.Set(ctx, key, value, expiration).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 删除缓存
func Del(ctx context.Context, key string) bool {
	redisClient := resource.RedisClient
	_, err := redisClient.Del(ctx, key).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 像集合中添加数据
func SAdd(ctx context.Context, key string, value any) bool {
	redisClient := resource.RedisClient
	_, err := redisClient.SAdd(ctx, key, value).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 获取集合中数据
func SMembers(ctx context.Context, key string) []string {
	redisClient := resource.RedisClient

	strSlice, err := redisClient.SMembers(ctx, key).Result()
	if err != nil && err != redis.Nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())

		return []string{}
	}

	return strSlice
}

// 删除集合中数据
func SRem(ctx context.Context, key string, members any) bool {
	redisClient := resource.RedisClient

	_, err := redisClient.SRem(ctx, key, members).Result()
	if err != nil && err != redis.Nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())

		return false
	}

	return true
}

// 向有序集合中添加数据
func ZAdd(ctx context.Context, key string, score float64, value any) bool {
	redisClient := resource.RedisClient

	members := &redis.Z{
		Score:  score,
		Member: value,
	}
	_, err := redisClient.ZAdd(ctx, key, members).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

func SetNX(ctx context.Context, key string, value interface{}, expire int) bool {
	redisClient := resource.RedisClient
	expiration := time.Second * time.Duration(expire)
	res, err := redisClient.SetNX(ctx, key, value, expiration).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}
	return res
}
