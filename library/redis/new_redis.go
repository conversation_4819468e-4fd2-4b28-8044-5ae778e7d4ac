package redis

import (
	"fmt"

	"github.com/go-redis/redis/v8"
	"icode.baidu.com/helix_web/helpers"
)

var redisClient *redis.Client

type Config struct {
	Host          string
	Port          int
	Password      string
	DB            int
	PoolSizePerIP int
}

func InitRedisClient(configFile string) {
	var redisConfig Config
	err := helpers.ReadTomlFile2(configFile, &redisConfig)
	if err != nil {
		panic(fmt.Sprintf("redis config read failed: %s", err.Error()))
	}

	redisClient = redis.NewClient(&redis.Options{
		Addr:     redisConfig.Host,
		Password: redisConfig.Password,
		DB:       redisConfig.DB,
		PoolSize: redisConfig.PoolSizePerIP,
	})

	_, err = redisClient.Ping(redisClient.Context()).Result()
	if err != nil {
		panic(fmt.Sprintf("redis connect failed: %s", err.<PERSON><PERSON><PERSON>()))
	}
}

func GetRedisClient() *redis.Client {
	return redisClient
}

func GetRedisConnection() *redis.Conn {
	return redisClient.Conn(redisClient.Context())
}
