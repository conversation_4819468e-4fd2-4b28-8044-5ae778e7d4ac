package bceaa

import (
	"io/ioutil"
	"strings"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/sts"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/library/def"
)

const (
	ObjectURLExpireTime = 300 // 300s

	EndpointBJ  = "bj.bcebos.com"
	EndpointSZ  = "su.bcebos.com"
	CdnEndpoint = "cdn.bcebos.com"

	bosAK = "ALTAKN6wG2vrEi7wVATa31qUk3"
	bosSK = "7f7d55a59a044cb39ae4ecb2b224b100"

	devDefaultBucket    = "bos://paddlehelix_bos"
	onlineDefaultBucket = "bos://paddlehelix_bos"
)

var defaultBucketMap = map[string]string{
	env.RunModeDebug:   devDefaultBucket,
	env.RunModeTest:    devDefaultBucket,
	env.RunModeRelease: onlineDefaultBucket,
}
var endpointMap = map[string]string{
	"bj": EndpointBJ,
	"su": EndpointSZ,
}

// 获取上传 token
func GetSessionToken() (map[string]any, error) {
	var resp map[string]any

	// 创建STS服务的Client对象，Endpoint使用默认值
	stsClient, err := sts.NewClient(bosAK, bosSK)
	if err != nil {
		return resp, err
	}

	// 获取临时认证token，有效期为60秒，ACL为空
	stsResult, err := stsClient.GetSessionToken(120, "")
	if err != nil {
		return resp, err
	}

	resp = map[string]any{
		"temp_ak":       stsResult.AccessKeyId,
		"temp_sk":       stsResult.SecretAccessKey,
		"session_token": stsResult.SessionToken,
		"endpoint":      EndpointBJ,
		"bucket":        defaultBucketMap[env.RunMode()],
	}
	return resp, nil
}

// 获取文件内容
func GetObject(bucket, object string) (string, error) {
	// 处理bucket
	if len(bucket) == 0 {
		bucket = defaultBucketMap[env.RunMode()]
	}

	bosClient, err := bos.NewClient(bosAK, bosSK, getEndpointByBucket(bucket))
	if err != nil {
		return "", err
	}

	objectRes, err := bosClient.BasicGetObject(bucket, object)
	if err != nil {
		return "", err
	}

	bodyData, err := ioutil.ReadAll(objectRes.Body)
	if err != nil {
		return "", err
	}
	defer objectRes.Body.Close()

	return string(bodyData), nil
}

// 获取访问链接
func GenerateObjectURL(bucket, object string, expireInSeconds def.IntType) (string, error) {
	bosClient, err := bos.NewClient(bosAK, bosSK, getEndpointByBucket(bucket))
	if err != nil {
		return "", err
	}

	objectURL := bosClient.BasicGeneratePresignedUrl(bucket, object, expireInSeconds)
	objectURL = strings.Replace(objectURL, "http:", "https:", 1)

	return objectURL, nil
}

func getEndpointByBucket(bucket string) string {
	bosClient, err := bos.NewClient(bosAK, bosSK, EndpointBJ)
	if err != nil {
		return EndpointBJ
	}

	location, err := bosClient.GetBucketLocation(bucket)
	if err != nil || (location != "bj" && location != "su") {
		return EndpointBJ
	}

	return endpointMap[location]
}
