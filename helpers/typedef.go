package helpers

import (
	"encoding/json"
	"errors"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"unicode/utf8"
)

type IntBuiltIn interface {
	int | int8 | int16 | int32 | int64
}

type IntegerString interface {
	IntBuiltIn | string
}

type IssetData struct {
	FormatData map[string]any
}

func ArrayUnique[T IntegerString](arr []T) []T {
	size := len(arr)
	result := make([]T, 0, size)
	temp := map[T]struct{}{}
	for i := 0; i < size; i++ {
		if _, ok := temp[arr[i]]; !ok {
			temp[arr[i]] = struct{}{}
			result = append(result, arr[i])
		}
	}
	return result
}

func ArraySlice(s []map[string]any, offset, length uint) []map[string]any {
	if offset > uint(len(s)) {
		return s[uint(len(s)):]
	}
	end := offset + length
	if end < uint(len(s)) {
		return s[offset:end]
	}
	return s[offset:]
}

func InArray[T IntegerString](needle T, hystack []T) bool {
	for _, item := range hystack {
		if needle == item {
			return true
		}
	}
	return false
}

func ArrayDiff[T IntegerString](s1, s2 []T) (diffArr []T) {
	temp := make(map[T]bool, len(s2))
	for _, val := range s2 {
		if _, ok := temp[val]; !ok {
			temp[val] = true
		}
	}

	for _, val := range s1 {
		if _, ok := temp[val]; !ok {
			diffArr = append(diffArr, val)
		}
	}
	return
}

func StructToStruct(source any, target any) error {
	sourceString := EncodeJSONStruct(source)
	err := DecodeJSONStruct(sourceString, target)
	return err
}

func EncodeJSONStruct(value any) string {
	encodeValue, err := json.Marshal(value)
	if err != nil {
		value = make(map[string]string)
		encodeValue, _ = json.Marshal(value)
	}
	return string(encodeValue)
}

func DecodeJSONStruct(value string, result any) error {
	err := json.Unmarshal([]byte(value), result)
	return err
}

func ArrayMerge(ss ...[]any) []any {
	n := 0
	for _, v := range ss {
		n += len(v)
	}
	s := make([]any, 0, n)
	for _, v := range ss {
		s = append(s, v...)
	}
	return s
}

func ArrayFlip[T1 IntegerString, T2 IntegerString](m map[T1]T2) map[T2]T1 {
	n := make(map[T2]T1)
	for i, v := range m {
		n[v] = i
	}
	return n
}

func Explode(delimiter, text string) []string {
	if len(text) == 0 {
		return []string{}
	}
	if len(delimiter) > len(text) {
		return strings.Split(delimiter, text)
	}
	return strings.Split(text, delimiter)
}

func HasEmoji(input string) bool {
	var tempRuneArr []rune
	for _, r := range []rune(input) {
		tempRuneArr = []rune{r}
		if len(string(tempRuneArr)) > 3 {
			return true
		}
	}

	return false
}

func GetStrLength(str string) int {
	strLen := utf8.RuneCountInString(str)
	return strLen
}

func ValidateString(str string, pattern string) bool {
	if matched, err := regexp.MatchString(pattern, str); err != nil || !matched {
		return false
	}
	return true
}

func MapsMerge(maps ...map[string]any) map[string]any {
	result := map[string]any{}
	for _, m := range maps {
		for k, v := range m {
			result[k] = v
		}
	}
	return result
}

func ArrayAssoc(arr []map[string]any, key string, val any, takeAll bool) map[any]any {
	ret := make(map[any]any)
	for _, item := range arr {
		resKey := item[key]
		resVal := item
		if val != nil {
			val = val.(string)
			resVal = item[val.(string)].(map[string]any)
		}
		if takeAll {
			keyArr, ok := resKey.([]any)
			if !ok {
				keyArr = []any{resKey}
			}
			for _, k := range keyArr {
				if _, ok = ret[k]; !ok {
					ret[k] = []any{}
				}
				ret[k] = append(ret[k].([]any), resVal)
			}
			continue
		}
		ret[resKey] = resVal
	}
	return ret
}

func GetColumn(arr any, key string) []any {
	sliceValue := reflect.ValueOf(arr)

	result := make([]any, 0, sliceValue.Len())
	for i := 0; i < sliceValue.Len(); i++ {
		itemValue := sliceValue.Index(i)
		var f reflect.Value
		switch itemValue.Kind() {
		case reflect.Struct:
			f = itemValue.FieldByName(key)
		case reflect.Slice:
			elemType := itemValue.Type().Elem()
			if elemType.Kind() == reflect.Struct || elemType.Kind() == reflect.Ptr {
				f = itemValue.Index(i).FieldByName(key)
			}
		case reflect.Interface:
			if val, ok := itemValue.Interface().(map[string]any); ok {
				// 通过 fieldName 获取对应的值
				if fieldValue, ok := val[key]; ok {
					result = append(result, fieldValue)
				}
			}
		case reflect.Map:
			if val, ok := itemValue.Interface().(map[string]any); ok {
				// 通过 fieldName 获取对应的值
				if fieldValue, ok := val[key]; ok {
					result = append(result, fieldValue)
				}
			}
		default:
			return result
		}
		if f.IsValid() {
			result = append(result, f.Interface())
		}
	}
	return result
}

func ArrayIntersect(a, b []any) []any {
	// 定义一个map，用于存放a中的元素
	m := make(map[any]bool)
	for _, v := range a {
		m[v] = true
	}
	// 定义一个结果slice
	res := make([]any, 0)
	// 遍历b中的元素，如果在a中出现，则加入结果
	for _, v := range b {
		if m[v] {
			res = append(res, v)
		}
	}
	return res
}

func InMap(key string, hystack map[string]any) bool {
	_, exists := hystack[key]
	if exists {
		return true
	}
	return false
}

func MergeMaps(maps ...map[string]any) map[string]any {
	result := make(map[string]any)

	for _, m := range maps {
		v := reflect.ValueOf(m)

		if v.Kind() != reflect.Map {
			continue
		}

		for _, key := range v.MapKeys() {
			result[key.Interface().(string)] = v.MapIndex(key).Interface()
		}
	}

	return result
}

func IsUTF8(s string) bool {
	return utf8.ValidString(s)
}

func ConvertToBool(value any) (bool, error) {
	switch v := value.(type) {
	case bool:
		return v, nil
	case string:
		return strconv.ParseBool(v)
	case int:
		return v != 0, nil
	case float32:
		return v != 0.0, nil
	case float64:
		return v != 0.0, nil
	default:
		return false, errors.New("unsupported")
	}
}

// GetArrayAssoc 构建关联数组
func GetArrayAssoc(slice any, fieldKey string, fieldValue string, takeAll bool) map[any]any {
	s := reflect.ValueOf(slice)
	result := make(map[any]any)
	for i := 0; i < s.Len(); i++ {
		value := s.Index(i)
		needKey := value.FieldByName(fieldKey).Interface()
		needValue := value.Interface()
		if len(fieldValue) != 0 {
			needValue = value.FieldByName(fieldValue).Interface()
		}
		if takeAll {
			if _, ok := result[needKey]; !ok {
				result[needKey] = make([]any, 0)
			}
			result[needKey] = append(result[needKey].([]any), needValue)
		}
		result[needKey] = needValue
	}
	return result
}

// ArrayValues 获取数组的value
func ArrayValues(input map[any]any) []any {
	values := make([]any, 0, len(input))
	for _, v := range input {
		values = append(values, v)
	}
	return values
}

func Arr2Str(arr []string, separator string) string {
	if len(arr) == 0 {
		return ""
	}
	return strings.Join(arr, separator)
}

// ValueDefault 获取一个接口类型变量的值，用默认值类型判断
func ValueDefault(ret, defaultValue any) any {
	switch defaultValue.(type) {
	case int:
		return AnyStrToInt(ret)
	case uint32:
		return uint32(AnyStrToInt(ret))
	case int32:
		return int32(AnyStrToInt(ret))
	case int64:
		return int64(AnyStrToInt(ret))
	case uint64:
		return uint64(AnyStrToInt(ret))
	case string:
		return ret
	case bool:
		return AnyStrToBool(ret)
	default:
		return defaultValue
	}
}

// AnyStrToInt 将接口类型的字符串转int
func AnyStrToInt(i any) int {
	s, ok := i.(string)
	if !ok {
		return 0
	}
	ret, _ := strconv.Atoi(s)
	return ret
}

// AnyStrToBool 将接口类型的字符串转bool
func AnyStrToBool(i any) bool {
	s, ok := i.(string)
	if !ok {
		return false
	}
	if len(s) == 0 {
		return false
	}

	if s[0] >= '0' && s[0] <= '9' {
		return Int2Bool(AnyStrToInt(i))
	}
	return strings.ToLower(s) == "true"
}

func Int2Bool(data int) bool {
	return data > 0
}

func InStringSlice(find string, slc []string) bool {
	if slc == nil || len(slc) <= 0 {
		return false
	}
	for i := range slc {
		if slc[i] == find {
			return true
		}
	}
	return false
}

func ToString(data any) string {
	b, _ := json.Marshal(data)
	return string(b)
}

func Isset(ObjectSet any) bool {
	if ObjectSet == nil {
		return false
	}
	return true
}
