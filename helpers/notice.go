package helpers

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"os"
	"strings"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	HelixNoticeWebhook = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd8a7d6ab288e3411b71f7ecdb763720d"

	HelixFold3ConcurrentTaskNoticeWebhook = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dff9a7ece26e941403ea4871df86e6bf3"

	InnerEmailNoticeUrl = "http://bjyz-sys-nsc02.bjyz.baidu.com:8605/api/openapi/send"
)

// helix notice
func HelixNotice(ctx context.Context, message string) {
	sendHiNotice(ctx, message, HelixNoticeWebhook, []string{})
}

// HelixFold3任务并发数量报警消息发送
func HelixFold3ConcurrentTaskNotice(ctx context.Context, message string) {
	sendHiNotice(ctx, message, HelixFold3ConcurrentTaskNoticeWebhook, []string{})
}

// send notice
func sendHiNotice(ctx context.Context, message string, webhook string, userIds []string) {
	hostname, _ := os.Hostname()
	logId := ""

	field := logit.FindLogIDField(ctx)
	if field != nil {
		logId, _ = field.Value().(string)
	}

	message = env.RunMode() + "--admin--[host:" + hostname + "]--[logid:" + logId + "]--" + message
	body := []map[string]any{
		{
			"content": message,
			"type":    "TEXT",
		},
	}
	if len(userIds) > 0 {
		body = append(body, map[string]any{
			"atuserids": userIds,
			"atall":     false,
			"type":      "AT",
		})
	}

	// 发送内容
	content := map[string]any{
		"message": map[string]any{
			"body": body,
		},
	}
	contentByte, err := json.Marshal(content)
	if err != nil {
		return
	}
	reqBody := strings.NewReader(string(contentByte))
	resp, err := http.Post(webhook, "application/json", reqBody)
	if err != nil {
		resource.LoggerService.Error(ctx, "------hi notice "+err.Error())
		return
	}
	defer resp.Body.Close()
}

// 发送邮件通知
func SendEmailNotice(noticeEmail, subject, content string) error {
	body := map[string]any{
		"noticeType":  "邮件通知",
		"noticeEmail": noticeEmail,
		"noticeMsg": map[string]string{
			"email":   content,
			"subject": subject,
		},
		"currentUser": "PaddleHelix",
	}

	bodyByte, err := json.Marshal(body)
	if err != nil {
		return err
	}

	reqBody := strings.NewReader(string(bodyByte))
	resp, err := http.Post(InnerEmailNoticeUrl, "application/json", reqBody)

	var ctx context.Context
	if err != nil {
		resource.LoggerService.Error(ctx, "------email notice "+err.Error())
		return err
	}
	defer resp.Body.Close()

	bodyData, _ := io.ReadAll(resp.Body)
	resource.LoggerService.Notice(ctx, "------email notice resp "+string(bodyData))
	return nil
}
