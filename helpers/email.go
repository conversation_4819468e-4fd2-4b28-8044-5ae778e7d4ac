package helpers

import (
	"net/smtp"

	"github.com/jordan-wright/email"
)

const (
	addr       = "smtp.126.com:25"
	smtpHost   = "smtp.126.com"
	authUser   = "<EMAIL>"
	authPasswd = "OIGKGSSJYWRNUZTC"

	FromUser = "<EMAIL>"
	CCUser   = "<EMAIL>"
)

// smtp 发送邮件
func SendMail(fromUser, toUser, subject, content, filePath string) error {
	e := email.NewEmail()
	e.From = fromUser
	e.To   = []string{toUser}
	e.Cc   = []string{CCUser}

	e.Subject = subject
	e.Text = []byte(content)

	// 以路径将文件作为附件添加到邮件中
	if len(filePath) > 0 {
		e.AttachFile(filePath)
	}

	// 发送邮件(passwd不是邮箱密码而是授权码)
	err := e.Send(addr, smtp.PlainAuth("", authUser, authPasswd, smtpHost))
	return err
}
