package helpers

import (
	"testing"
)

func TestFormatFloat(t *testing.T) {
	tests := []struct {
		input  float64
		output float64
	}{
		{666.3232342, 666.3232},
		{666.32, 666.3200},
		{666.555555, 666.5556},
	}

	for _, test := range tests {
		if got := FormatFloat(test.input, 4); got != test.output {
			t.<PERSON><PERSON>("output(%v)", got)
		}
	}
}

func TestAssertString(t *testing.T) {
	tests := []struct {
		input  interface{}
		output string
	}{
		{121312, ""},
		{"ddd", "ddd"},
		{[]int{1, 2, 3}, ""},
	}

	for _, test := range tests {
		if got := AssertString(test.input); got != test.output {
			t.<PERSON>rrorf("output(%v)", got)
		}
	}
}

func TestAssertFloat64(t *testing.T) {
	tests := []struct {
		input  interface{}
		output float64
	}{
		{121312, 0},
		{float64(12.12), float64(12.12)},
		{[]int{1, 2, 3}, 0},
	}

	for _, test := range tests {
		if got := AssertFloat64(test.input); got != test.output {
			t.<PERSON><PERSON>rf("output(%v)", got)
		}
	}
}
