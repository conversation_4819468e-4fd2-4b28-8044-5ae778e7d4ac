package helpers

import (
	"fmt"

	"github.com/go-playground/validator/v10"
)

// Validate 返回参数校验失败的错误码和错误字段
func Validate(err error) string {
	invalidErr, ok := err.(*validator.InvalidValidationError)
	if ok {
		return fmt.Sprintf("param error: %s", invalidErr.Error())
	}

	validationErrs, ok := err.(validator.ValidationErrors)
	if ok {
		for _, validationErr := range validationErrs {
			return fmt.Sprintf("param error: %s", validationErr.Error())
		}
	}

	return "param error"
}
