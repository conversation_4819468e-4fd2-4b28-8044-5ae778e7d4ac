package helpers

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/base64"
	"errors"
	"fmt"
	"image/color"
	"image/png"
	"math/rand"
	"os/exec"
	"runtime"
	"strconv"
	"strings"

	"github.com/afocus/captcha"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/library/def"
	"icode.baidu.com/helix_web/library/resource"
)

func GetLogId(ctx context.Context) string {
	field := logit.FindLogIDField(ctx)
	if field != nil {
		logId := field.Value().(string)
		return logId
	}

	return ""
}

// defer 函数
func DeferFunc(ctx context.Context) func() {
	return func() {
		if err := recover(); err != nil {
			trace := make([]byte, 4096)
			runtime.Stack(trace[:], false)
			title := fmt.Sprintf("panic:%v", err)

			// 堆栈
			traceStr := string(trace)
			logFields := []logit.Field{
				logit.String("panic_trace", traceStr),
			}

			resource.LoggerService.Error(ctx, title, logFields...)
			HelixNotice(ctx, "--- goruntime error"+title)
		}
	}
}

// 写错误日志
func LogError(ctx context.Context, err error) {
	if err == nil {
		return
	}

	trace := make([]byte, 4096)
	runtime.Stack(trace[:], false)
	title := fmt.Sprintf("%v", err)

	// 堆栈
	traceStr := string(trace)

	logFields := []logit.Field{
		logit.String("trace", traceStr),
	}
	resource.LoggerService.Error(ctx, title, logFields...)
}

// 写 notice 日志
func LogNotice(ctx context.Context, msg string) {
	if msg == "" {
		return
	}
	resource.LoggerService.Notice(ctx, msg)
}

// 处理bos fileUrl 地址
func DealBosFileUrl(fileUrl string) (string, string) {
	path := strings.Replace(fileUrl, "bos:/", "", 1)

	index := strings.Index(path, "/")
	if index <= 0 {
		return "", ""
	}
	bucket := path[0:index]
	object := path[index+1:]

	return bucket, object
}

// 生成验证码图片
func GenerateCaptcha(number def.IntType) (string, string, error) {
	cap := captcha.New()
	if err := cap.SetFont(env.DataDir() + "/font/comic.ttf"); err != nil {
		return "", "", err
	}

	cap.SetSize(128, 64)
	cap.SetBkgColor(color.RGBA{221, 221, 221, 221})
	img, str := cap.Create(number, captcha.ALL)

	emptyBuff := bytes.NewBuffer(nil) //开辟一个新的空buff
	err := png.Encode(emptyBuff, img)
	if err != nil {
		return "", "", err
	}

	dist := make([]byte, 50000)                        //开辟存储空间
	base64.StdEncoding.Encode(dist, emptyBuff.Bytes()) //buff转成base64
	index := bytes.IndexByte(dist, 0)                  //这里要注意，因为申请的固定长度数组
	baseImage := dist[0:index]

	if string(baseImage) == "" || str == "" {
		return "", "", errors.New("generate image error")
	}
	return string(baseImage), str, nil
}

// 获取随机字符串
func GetRandomString(n def.IntType) string {
	str := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	byteStr := []byte(str)
	var result []byte
	for i := 0; i < n; i++ {
		result = append(result, byteStr[rand.Intn(len(byteStr))])
	}
	return string(result)
}

// 执行shell
func Command(cmd string) (string, error) {
	c := exec.Command("bash", "-c", cmd)
	output, err := c.CombinedOutput()

	return string(output), err
}

// 获取 md5 值
func Md5(str string) string {
	hash := md5.New()
	hash.Write([]byte(str))
	sum := hash.Sum(nil)

	return fmt.Sprintf("%x", sum)
}

// trim
func Trim(str string) string {
	return strings.TrimSpace(str)
}

// 获取数字随机字符串
func GetNumRandomStr(n int) int {
	str := "123456789"
	byteStr := []byte(str)
	var result []byte
	for i := 0; i < n; i++ {
		result = append(result, byteStr[rand.Intn(len(byteStr))])
	}
	num, _ := strconv.Atoi(string(result))
	return num
}
