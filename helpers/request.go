package helpers

import (
	"encoding/json"
	"io"
	"io/ioutil"
	"strings"
)

// 获取 body 内容
func GetBodyParams(body io.ReadCloser) map[string]any {
	var bodyParams map[string]any
	if body == nil {
		return bodyParams
	}

	bodyData, err := ioutil.ReadAll(body)
	if err != nil {
		return bodyParams
	}
	defer body.Close()

	_ = json.Unmarshal(bodyData, &bodyParams)
	return bodyParams
}

// 获取 int 参数
func GetIntParam(params map[string]any, name string, defaultValue int64) int64 {
	value := defaultValue
	if valueFloat, ok := params[name]; ok {
		if valueF, ok := valueFloat.(float64); ok {
			value = int64(valueF)
		}
	}

	return value
}

// 获取bool 参数
func GetBoolParam(params map[string]any, name string, defaultValue bool) bool {
	value := defaultValue
	if valueFloat, ok := params[name]; ok {
		if valueF, ok := valueFloat.(bool); ok {
			value = valueF
		}
	}

	return value
}

// 获取 string 参数
func GetStringParam(params map[string]any, name string) string {
	var value string
	if valueString, ok := params[name]; ok {
		if valueS, ok := valueString.(string); ok {
			value = strings.Trim(valueS, " ")
		}
	}

	return value
}

// 获取 map 参数
func GetMapParam(params map[string]any, name string) map[string]any {
	var value map[string]any
	if valueMap, ok := params[name]; ok {
		if valueM, ok := valueMap.(map[string]any); ok {
			value = valueM
		}
	}

	return value
}

// 获取 int 切片
func GetIntSliceParam(params map[string]any, name string) []int64 {
	var value []int64
	if valueSlice, ok := params[name]; ok {
		valueS, ok := valueSlice.([]any)
		if !ok {
			return value
		}

		for _, val := range valueS {
			if valNeed, ok := val.(float64); ok {
				value = append(value, int64(valNeed))
			}
		}
	}

	return value
}
