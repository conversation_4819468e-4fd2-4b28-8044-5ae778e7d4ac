package helpers

import (
	"context"
	"io"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/library/resource"
)

const (
	SuccessCode      = 0
	CommonErrorCode  = 1001
	ParamErrorCode   = 2001
	LoginErrorCode   = 3001
	ServiceErrorCode = 4001
	LogicErrorCode   = 5001
	DBErrorCode      = 6001
)

type JsonResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data"`
}

func SuccReturn(data any) ghttp.Response {
	return ghttp.NewJSONResponse(200, JsonResp{
		Code: SuccessCode,
		Msg:  "",
		Data: data,
	})
}

// 返回异常,报错信息打印
func ReturnLogError(ctx context.Context, err error) {
	resource.LoggerService.Error(ctx, err.Error())
}

func FailReturn(code int, msg string) ghttp.Response {
	return ghttp.NewJSONResponse(200, JsonResp{
		Code: code,
		Msg:  msg,
		Data: nil,
	})
}

func StreamReturn(rd io.Reader, headers map[string][]string) ghttp.Response {
	return &ghttp.StreamResponse{
		StatusCode:  200,
		Headers:     headers,
		Reader:      rd,
		ContentType: "application/octet-stream; charset=utf-8",
	}
}
