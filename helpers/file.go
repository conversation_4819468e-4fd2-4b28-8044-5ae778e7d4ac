package helpers

import (
	"errors"
	"os"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/conf"
)

// 读取 toml 文件
func ReadTomlFile(filePath string) (map[string]any, error) {
	var fileMap map[string]any

	confPath, err := filepath.Abs(filePath)
	if err != nil {
		return fileMap, err
	}

	err = conf.Parse(confPath, &fileMap)
	if err != nil {
		return fileMap, err
	}
	if len(fileMap) <= 0 {
		return fileMap, errors.New(filePath + " file is empty")
	}

	return fileMap, nil
}

// 读取 toml 文件
func ReadTomlFile2(filePath string, res any) error {
	confPath, err := filepath.Abs(filePath)
	if err != nil {
		return err
	}

	err = conf.Parse(confPath, res)
	if err != nil {
		return err
	}

	return nil
}

// 获取配置文件中的 field
func GetConfField(filePath, field string) (string, error) {
	confMap, err := ReadTomlFile(filePath)
	if err != nil {
		return "", err
	}

	content, ok := confMap[field].(string)
	if !ok || len(content) <= 0 {
		return "", errors.New(filePath + " " + field + " empty")
	}

	return content, nil
}

func WriteFile(filePath string, content string) error {
	data := []byte(content)

	// write to file
	err := os.WriteFile(filePath, data, 0644)
	if err != nil {
		return err
	}

	return nil
}
