package helpers

import (
	"time"

	"icode.baidu.com/helix_web/library/def"
)

const (
	TimeFormat = "2006-01-02 15:04:05"
)

// 时间戳转时间字符串
func UnixToTimeStr(unix def.TimestampType) string {
	if unix <= 0 {
		return ""
	}
	return time.Unix(unix, 0).Format(TimeFormat)
}

// 时间戳转日期字符串
func UnixToDateStr(unix def.TimestampType) string {
	if unix <= 0 {
		return ""
	}
	return time.Unix(unix, 0).Format("2006-01-02")
}

// 时间戳转时间time
func UnixToTime(unix def.TimestampType) time.Time {
	return time.Unix(unix, 0)
}

// 时间字符串转时间戳
func TimeStrToUnix(timeStr string) def.TimestampType {
	t, _ := time.Parse(TimeFormat, timeStr)
	return t.Unix()
}
