package helpers

// Contain check if the target value is in the slice or not.
func Contain[T comparable](slice []T, target T) bool {
	for _, item := range slice {
		if item == target {
			return true
		}
	}

	return false
}

// ColumnList converts a slice to a slice based on a callback function.
func ColumnList[T any, U comparable](slice []T, iteratee func(item T) U) []U {
	var result []U
	for _, v := range slice {
		u := iteratee(v)
		result = append(result, u)
	}

	return result
}

// KeyBy converts a slice to a map based on a callback function.
func KeyBy[T any, U comparable](slice []T, iteratee func(item T) U) map[U]T {
	result := make(map[U]T, len(slice))

	for _, v := range slice {
		k := iteratee(v)
		result[k] = v
	}

	return result
}

func SliceIntersect[T comparable](s1 []T, s2 []T) []T {
	var res []T

	if s1 == nil || s2 == nil || len(s1) == 0 || len(s2) == 0 {
		return res
	}

	for _, value := range s1 {
		if Contain[T](s2, value) {
			res = append(res, value)
		}
	}

	return res
}
