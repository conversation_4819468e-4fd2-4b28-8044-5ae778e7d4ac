package helpers

import (
	"bytes"
	"encoding/json"
	"io"
	"io/ioutil"
	"reflect"
	"strings"
	"testing"
)

func TestGetBodyParams(t *testing.T) {
	var needOutput map[string]any

	input1, _ := json.<PERSON>(map[string]any{"name": "nick", "age": 18})
	input2, _ := json.Marshal(map[string]any{"name": "nick", "other": map[string]any{"age": 18, "sex": 1}})
	tests := []struct {
		input  io.ReadCloser
		output map[string]any
	}{
		{ioutil.NopCloser(bytes.NewReader(input1)), map[string]any{
			"name": "nick",
			"age":  float64(18),
		}},
		{ioutil.NopCloser(bytes.NewReader(input2)), map[string]any{
			"name": "nick",
			"other": map[string]any{
				"age": float64(18),
				"sex": float64(1),
			},
		}},
		{nil, needOutput},
		{ioutil.NopCloser(strings.<PERSON><PERSON>eader("")), needOutput},
	}

	for _, test := range tests {
		if got := GetBodyParams(test.input); !reflect.DeepEqual(got, test.output) {
			t.<PERSON>("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetBoolParam(t *testing.T) {
	tests := []struct {
		input  map[string]any
		output bool
	}{
		{map[string]any{"is_check": true}, true},
		{map[string]any{"is_check": false}, false},
		{map[string]any{"is_check": 1}, false},
	}

	for _, test := range tests {
		if got := GetBoolParam(test.input, "is_check", false); got != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetIntParam(t *testing.T) {
	tests := []struct {
		input  map[string]any
		output int64
	}{
		{map[string]any{"age": true}, 0},
		{map[string]any{"age": float64(12)}, int64(12)},
		{map[string]any{"age": 1.98}, int64(1)},
	}

	for _, test := range tests {
		if got := GetIntParam(test.input, "age", 0); got != test.output {
			t.Errorf("want(%d), got(%d)", test.output, got)
		}
	}
}
