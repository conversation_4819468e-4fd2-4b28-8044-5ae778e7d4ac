package helpers

import (
	"fmt"
	"strconv"

	"icode.baidu.com/helix_web/library/def"
)

// 格式化 float 64 保留小数
func FormatFloat(value float64, decimal def.IntType) float64 {
	val, _ := strconv.ParseFloat(fmt.Sprintf("%."+strconv.Itoa(decimal)+"f", value), 64)
	return val
}

// 断言字符串
func AssertString(value interface{}) string {
	val, _ := value.(string)
	return val
}

// 断言float64
func AssertFloat64(value interface{}) float64 {
	val, _ := value.(float64)
	return val
}

func GetValByKey[K comparable, T any](m map[K]T, key K) T {
	var val T
	if _, ok := m[key]; ok {
		val = m[key]
	}

	return val
}
